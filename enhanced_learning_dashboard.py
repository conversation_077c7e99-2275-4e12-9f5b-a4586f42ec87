"""
Enhanced Learning Dashboard
GUI interface for monitoring and controlling the enhanced learning system
"""

import sys
import json
from datetime import datetime
from typing import Dict, List, Any
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                           QTextEdit, QTableWidget, QTableWidgetItem, 
                           QGroupBox, QProgressBar, QComboBox, QSpinBox,
                           QCheckBox, QMessageBox, QFileDialog, QSplitter)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor

from learning_system_integration import learning_integrator
from enhanced_adaptive_learning_v2 import enhanced_learning_system_v2
from robust_validation_system import robust_validator
from sklearn.model_selection import TimeSeriesSplit


def check_validation_readiness() -> Dict[str, Any]:
    """
    Check if the system is truly ready for validation by considering:
    1. Total AI predictions count
    2. Per-segment data availability
    3. TimeSeriesSplit fold requirements

    Returns:
        Dict with status, message, color, and detailed info
    """
    try:
        # Get all AI predictions
        all_predictions = learning_integrator.learning_system.prediction_tracker.predictions
        ai_predictions = [p for p in all_predictions if getattr(p, 'is_ai_prediction', False)]
        ai_count = len(ai_predictions)

        # Get research-based minimum from enhanced learning system
        try:
            from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
            enhanced_system = EnhancedAdaptiveLearningSystem()
            min_required = enhanced_system.min_sample_size  # Research-based: 200
        except:
            min_required = 200  # Fallback to research-based minimum

        # Basic count check using research-based minimum
        if ai_count < min_required:
            return {
                'status': 'insufficient_data',
                'message': f"Insufficient Data ({ai_count}/{min_required} AI predictions needed)",
                'color': 'orange',
                'details': {
                    'ai_predictions': ai_count,
                    'min_required': min_required,
                    'segments_tested': 0,
                    'validation_viable': False
                }
            }

        # Check validation viability using the same logic as robust validation
        filtered_predictions = robust_validator._filter_predictions(ai_predictions)

        if len(filtered_predictions) < robust_validator.config.min_train_size + robust_validator.config.min_test_size:
            return {
                'status': 'insufficient_filtered_data',
                'message': f"Insufficient Quality Data ({len(filtered_predictions)}/{robust_validator.config.min_train_size + robust_validator.config.min_test_size} needed after filtering)",
                'color': 'orange',
                'details': {
                    'ai_predictions': ai_count,
                    'filtered_predictions': len(filtered_predictions),
                    'min_required': robust_validator.config.min_train_size + robust_validator.config.min_test_size,
                    'segments_tested': 0,
                    'validation_viable': False
                }
            }

        # Check segment-level validation viability
        segmented_data = robust_validator._segment_predictions(filtered_predictions)
        viable_segments = 0
        total_segments = len(segmented_data)
        segment_details = {}

        for segment_key, segment_predictions in segmented_data.items():
            segment_size = len(segment_predictions)

            # Check if segment has enough data for basic validation
            has_enough_basic = segment_size >= robust_validator.config.min_train_size + robust_validator.config.min_test_size

            # Check TimeSeriesSplit viability
            valid_folds = 0
            if has_enough_basic:
                # Sort by timestamp like validation does
                segment_predictions.sort(key=lambda x: x.timestamp)

                # Test TimeSeriesSplit
                tscv = TimeSeriesSplit(n_splits=robust_validator.config.n_splits)
                splits = list(tscv.split(segment_predictions))

                for train_indices, test_indices in splits:
                    if (len(train_indices) >= robust_validator.config.min_train_size and
                        len(test_indices) >= robust_validator.config.min_test_size):
                        valid_folds += 1

            is_viable = valid_folds > 0
            if is_viable:
                viable_segments += 1

            segment_details[segment_key] = {
                'size': segment_size,
                'has_enough_basic': has_enough_basic,
                'valid_folds': valid_folds,
                'total_folds': robust_validator.config.n_splits,
                'is_viable': is_viable
            }

        # Determine overall status
        if viable_segments == 0:
            # Calculate minimum needed for viable validation
            # For TimeSeriesSplit with 5 folds, we need enough data so that test sets have ≥15 predictions
            # Rough estimate: need ~75-80 predictions per segment
            min_needed_per_segment = robust_validator.config.min_test_size * robust_validator.config.n_splits + robust_validator.config.min_train_size

            return {
                'status': 'validation_not_viable',
                'message': f"Collecting Data - Validation may fail (need ~{min_needed_per_segment} per segment for reliable TimeSeriesSplit)",
                'color': '#FFA500',  # Orange
                'details': {
                    'ai_predictions': ai_count,
                    'filtered_predictions': len(filtered_predictions),
                    'total_segments': total_segments,
                    'viable_segments': viable_segments,
                    'validation_viable': False,
                    'segments': segment_details,
                    'min_needed_per_segment': min_needed_per_segment
                }
            }
        else:
            return {
                'status': 'ready',
                'message': f"Ready for Validation ({viable_segments}/{total_segments} segments viable)",
                'color': 'blue',
                'details': {
                    'ai_predictions': ai_count,
                    'filtered_predictions': len(filtered_predictions),
                    'total_segments': total_segments,
                    'viable_segments': viable_segments,
                    'validation_viable': True,
                    'segments': segment_details
                }
            }

    except Exception as e:
        return {
            'status': 'error',
            'message': f"Error checking validation readiness: {str(e)}",
            'color': 'red',
            'details': {
                'error': str(e),
                'validation_viable': False
            }
        }


class LearningStatusWidget(QWidget):
    """Widget showing current learning system status"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_status)
        self.refresh_timer.start(5000)  # Refresh every 5 seconds
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Overall status
        self.overall_group = QGroupBox("Overall Learning Status")
        overall_layout = QVBoxLayout()
        
        self.total_predictions_label = QLabel("Total Predictions: 0")
        self.segments_with_weights_label = QLabel("Segments with Learned Weights: 0")
        self.last_update_label = QLabel("Last Learning Update: Never")
        self.reliability_score_label = QLabel("System Reliability: Unknown")
        self.reliability_score_label.setStyleSheet("text-decoration: underline; font-weight: bold;")
        self.reliability_score_label.mousePressEvent = self.show_validation_readiness_details
        self.last_readiness_check = None
        
        overall_layout.addWidget(self.total_predictions_label)
        overall_layout.addWidget(self.segments_with_weights_label)
        overall_layout.addWidget(self.last_update_label)
        overall_layout.addWidget(self.reliability_score_label)
        
        self.overall_group.setLayout(overall_layout)
        layout.addWidget(self.overall_group)
        
        # Segment status table
        self.segments_group = QGroupBox("Segment Status")
        segments_layout = QVBoxLayout()
        
        self.segments_table = QTableWidget()
        self.segments_table.setColumnCount(6)
        self.segments_table.setHorizontalHeaderLabels([
            "Segment", "Total Predictions", "Completed", "Min Required", 
            "Ready for Learning", "Has Learned Weights"
        ])
        
        segments_layout.addWidget(self.segments_table)
        self.segments_group.setLayout(segments_layout)
        layout.addWidget(self.segments_group)
        
        # Control buttons
        controls_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("Refresh Status")
        self.refresh_button.clicked.connect(self.refresh_status)
        
        self.force_learning_button = QPushButton("Force Learning")
        self.force_learning_button.clicked.connect(self.force_learning_dialog)
        
        self.reset_button = QPushButton("Reset Learning Data")
        self.reset_button.clicked.connect(self.reset_learning_dialog)
        
        controls_layout.addWidget(self.refresh_button)
        controls_layout.addWidget(self.force_learning_button)
        controls_layout.addWidget(self.reset_button)
        
        layout.addLayout(controls_layout)
        self.setLayout(layout)
        
        # Initial refresh
        self.refresh_status()
    
    def refresh_status(self):
        """Refresh the learning status display"""
        try:
            status = learning_integrator.get_integration_status()
            learning_status = status.get('learning_system_status', {})
            overall_stats = learning_status.get('overall_stats', {})
            
            # Update overall status
            ai_predictions = overall_stats.get('total_predictions', 0)
            all_predictions = overall_stats.get('total_all_predictions', 0)
            self.total_predictions_label.setText(f"Total AI Predictions: {ai_predictions} (Total: {all_predictions})")
            self.segments_with_weights_label.setText(f"Segments with Learned Weights: {overall_stats.get('segments_with_learned_weights', 0)}")

            last_update = overall_stats.get('last_learning_update', 'Never')
            if last_update != 'Never':
                try:
                    # Format datetime for display
                    dt = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                    last_update = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    pass
            self.last_update_label.setText(f"Last Learning Update: {last_update}")

            # Enhanced reliability status using comprehensive validation readiness check
            readiness_info = check_validation_readiness()
            reliability_text = f"System Reliability: {readiness_info['message']}"
            self.reliability_score_label.setText(reliability_text)
            self.reliability_score_label.setStyleSheet(f"color: {readiness_info['color']};")

            # Store readiness details for potential tooltip or detailed view
            self.last_readiness_check = readiness_info
            
            # Update segments table
            segments = learning_status.get('segments', {})
            self.segments_table.setRowCount(len(segments))
            
            for row, (segment_key, segment_data) in enumerate(segments.items()):
                self.segments_table.setItem(row, 0, QTableWidgetItem(segment_key))
                self.segments_table.setItem(row, 1, QTableWidgetItem(str(segment_data.get('total_predictions', 0))))
                self.segments_table.setItem(row, 2, QTableWidgetItem(str(segment_data.get('completed_predictions', 0))))
                self.segments_table.setItem(row, 3, QTableWidgetItem(str(segment_data.get('min_required_for_learning', 0))))
                
                ready = "Yes" if segment_data.get('ready_for_learning', False) else "No"
                ready_item = QTableWidgetItem(ready)
                if ready == "Yes":
                    ready_item.setBackground(QColor(144, 238, 144))  # Light green
                else:
                    ready_item.setBackground(QColor(255, 182, 193))  # Light red
                self.segments_table.setItem(row, 4, ready_item)
                
                has_weights = "Yes" if segment_data.get('has_learned_weights', False) else "No"
                weights_item = QTableWidgetItem(has_weights)
                if has_weights == "Yes":
                    weights_item.setBackground(QColor(173, 216, 230))  # Light blue
                self.segments_table.setItem(row, 5, weights_item)
            
            self.segments_table.resizeColumnsToContents()
            
        except Exception as e:
            print(f"Error refreshing status: {e}")

    def show_validation_readiness_details(self, event):
        """Show detailed validation readiness information"""
        if not hasattr(self, 'last_readiness_check') or not self.last_readiness_check:
            QMessageBox.information(self, "Validation Readiness", "No readiness information available.")
            return

        details = self.last_readiness_check.get('details', {})
        status = self.last_readiness_check.get('status', 'unknown')

        # Build detailed message
        message_parts = [
            f"Status: {status}",
            f"AI Predictions: {details.get('ai_predictions', 0)}",
        ]

        if 'filtered_predictions' in details:
            message_parts.append(f"Quality Predictions: {details['filtered_predictions']}")

        if 'total_segments' in details:
            message_parts.append(f"Data Segments: {details['total_segments']}")
            message_parts.append(f"Validation-Viable Segments: {details.get('viable_segments', 0)}")

        if 'min_needed_per_segment' in details:
            message_parts.append(f"Recommended per segment: ~{details['min_needed_per_segment']} predictions")

        # Add segment details if available
        segments = details.get('segments', {})
        if segments:
            message_parts.append("\nSegment Details:")
            for segment_key, segment_info in segments.items():
                viable_status = "✅ Viable" if segment_info.get('is_viable', False) else "❌ Not viable"
                valid_folds = segment_info.get('valid_folds', 0)
                total_folds = segment_info.get('total_folds', 5)
                message_parts.append(f"  {segment_key}: {segment_info.get('size', 0)} predictions, {valid_folds}/{total_folds} valid folds - {viable_status}")

        # Show explanation based on status
        if status == 'validation_not_viable':
            message_parts.append("\nℹ️ TimeSeriesSplit with 5 folds creates test sets that are too small.")
            message_parts.append("Each test set needs ≥15 predictions for reliable validation.")
        elif status == 'ready':
            message_parts.append("\n✅ System is ready for comprehensive validation!")

        detailed_message = "\n".join(message_parts)

        QMessageBox.information(self, "Validation Readiness Details", detailed_message)

    def force_learning_dialog(self):
        """Show dialog to force learning for a segment"""
        # Get available segments
        status = learning_integrator.get_integration_status()
        segments = status.get('learning_system_status', {}).get('segments', {})
        
        if not segments:
            QMessageBox.information(self, "No Segments", "No segments available for learning.")
            return
        
        # Create simple selection dialog
        segment_keys = list(segments.keys())
        from PyQt5.QtWidgets import QInputDialog
        
        segment_key, ok = QInputDialog.getItem(
            self, "Force Learning", "Select segment to force learning:", 
            segment_keys, 0, False
        )
        
        if ok and segment_key:
            try:
                result = enhanced_learning_system_v2.force_learning_for_segment(segment_key)
                if result['status'] == 'learning_triggered':
                    QMessageBox.information(self, "Success", f"Learning triggered for {segment_key}")
                else:
                    QMessageBox.warning(self, "Failed", f"Could not trigger learning: {result.get('status', 'Unknown error')}")
                
                self.refresh_status()
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error forcing learning: {str(e)}")
    
    def reset_learning_dialog(self):
        """Show dialog to reset learning data"""
        reply = QMessageBox.question(
            self, "Reset Learning Data", 
            "Are you sure you want to reset all learning data? This cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                learning_integrator.reset_learning_system()
                QMessageBox.information(self, "Success", "Learning data has been reset.")
                self.refresh_status()
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error resetting learning data: {str(e)}")


class ValidationWidget(QWidget):
    """Widget for running and viewing validation results"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.run_validation_button = QPushButton("Run Comprehensive Validation")
        self.run_validation_button.clicked.connect(self.run_validation)

        self.run_balance_validation_button = QPushButton("Run Balance Validation")
        self.run_balance_validation_button.clicked.connect(self.run_balance_validation)
        self.run_balance_validation_button.setStyleSheet("QPushButton { background-color: #2E8B57; color: white; font-weight: bold; }")
        self.run_balance_validation_button.setToolTip("Run robust validation for Historical vs Momentum balance ratios")

        self.export_report_button = QPushButton("Export Full Report")
        self.export_report_button.clicked.connect(self.export_report)

        controls_layout.addWidget(self.run_validation_button)
        controls_layout.addWidget(self.run_balance_validation_button)
        controls_layout.addWidget(self.export_report_button)
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Results display
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setFont(QFont("Courier", 10))
        
        layout.addWidget(self.results_text)
        self.setLayout(layout)
    
    def run_validation(self):
        """Run comprehensive validation"""
        self.results_text.clear()
        self.results_text.append("Running comprehensive validation...")
        QApplication.processEvents()
        
        try:
            result = learning_integrator.run_comprehensive_validation()
            
            if result.get('status') == 'insufficient_data':
                self.results_text.append(f"\nINSUFFICIENT DATA:")
                self.results_text.append(f"Total predictions: {result.get('total_predictions', 0)}")
                self.results_text.append(f"Minimum required: {result.get('minimum_required', 200)}")  # Updated to research-based minimum
                self.results_text.append(f"Message: {result.get('message', '')}")
                return
            
            validation_result = result.get('validation_result', {})
            recommendations = result.get('integration_recommendations', [])
            
            # Display overall summary
            overall_summary = validation_result.get('overall_summary', {})
            self.results_text.append(f"\n=== OVERALL VALIDATION RESULTS ===")
            self.results_text.append(f"Total segments tested: {overall_summary.get('total_segments_tested', 0)}")
            self.results_text.append(f"Statistically significant segments: {overall_summary.get('statistically_significant_segments', 0)}")
            self.results_text.append(f"Overall accuracy: {overall_summary.get('overall_accuracy', 0.0):.3f}")
            self.results_text.append(f"System reliability score: {overall_summary.get('system_reliability_score', 0.0):.3f}")
            
            # Display segment results
            segment_results = validation_result.get('segment_results', {})
            if segment_results:
                self.results_text.append(f"\n=== SEGMENT RESULTS ===")
                for segment_key, segment_data in segment_results.items():
                    self.results_text.append(f"\nSegment: {segment_key}")
                    
                    cv_data = segment_data.get('cross_validation', {})
                    self.results_text.append(f"  Cross-validation accuracy: {cv_data.get('average_accuracy', 0.0):.3f} ± {cv_data.get('std_accuracy', 0.0):.3f}")
                    
                    bootstrap_data = segment_data.get('bootstrap_validation', {})
                    self.results_text.append(f"  Bootstrap accuracy: {bootstrap_data.get('mean_accuracy', 0.0):.3f}")
                    self.results_text.append(f"  95% CI: {bootstrap_data.get('confidence_interval_95', (0, 0))}")
                    self.results_text.append(f"  Statistically significant: {bootstrap_data.get('is_statistically_significant', False)}")
                    self.results_text.append(f"  Recommendation: {segment_data.get('recommendation', 'Unknown')}")
            
            # Display recommendations
            if recommendations:
                self.results_text.append(f"\n=== RECOMMENDATIONS ===")
                for i, rec in enumerate(recommendations, 1):
                    self.results_text.append(f"{i}. {rec}")
            
        except Exception as e:
            self.results_text.append(f"\nERROR: {str(e)}")

    def run_balance_validation(self):
        """Run robust balance validation for Historical vs Momentum ratios"""
        self.results_text.clear()
        self.results_text.append("🚀 Starting Robust Balance Validation for Historical vs Momentum Ratios...")
        self.results_text.append("=" * 80)
        QApplication.processEvents()

        try:
            # Import the enhanced learning system
            from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem

            # Create enhanced learning system instance
            enhanced_system = EnhancedAdaptiveLearningSystem()

            self.results_text.append("📊 Checking data availability...")
            QApplication.processEvents()

            # Run the robust balance validation
            validation_results = enhanced_system.run_robust_balance_validation()

            # Display results
            if validation_results.get('status') == 'insufficient_data':
                self.results_text.append(f"\n❌ INSUFFICIENT DATA:")
                self.results_text.append(f"   Completed predictions: {validation_results.get('completed_predictions', 0)}")
                self.results_text.append(f"   Total predictions: {validation_results.get('total_predictions', 0)}")
                self.results_text.append(f"   Required minimum: 150")
                self.results_text.append(f"\n💡 Make more predictions with the Enhanced Learning System to gather sufficient data.")
                return

            elif validation_results.get('status') == 'operation_denied':
                self.results_text.append(f"\n🛡️ OPERATION DENIED:")
                self.results_text.append(f"   {validation_results.get('message', 'Unknown reason')}")
                coordinator_status = validation_results.get('coordinator_status', {})
                if coordinator_status.get('accuracy_protection_active'):
                    self.results_text.append(f"   🔒 Accuracy drop protection is currently active")
                    self.results_text.append(f"   ⏳ Wait for protection period to end before running validation")
                return

            elif validation_results.get('status') == 'validation_error':
                self.results_text.append(f"\n❌ VALIDATION ERROR:")
                self.results_text.append(f"   {validation_results.get('error', 'Unknown error')}")
                return

            # Display successful validation results
            self.results_text.append("\n✅ BALANCE VALIDATION COMPLETED SUCCESSFULLY!")
            self.results_text.append("=" * 80)

            overall_summary = validation_results.get('overall_summary', {})
            context_results = validation_results.get('context_results', {})

            # Overall metrics
            self.results_text.append(f"\n📊 OVERALL RESULTS:")
            self.results_text.append(f"   Overall Accuracy: {overall_summary.get('overall_accuracy', 0.0):.3f}")
            self.results_text.append(f"   Contexts Tested: {overall_summary.get('contexts_tested', 0)}")
            self.results_text.append(f"   Statistically Significant: {overall_summary.get('statistically_significant_contexts', 0)}")
            self.results_text.append(f"   System Reliability: {overall_summary.get('system_reliability_score', 0.0):.3f}")

            # Context-specific results
            self.results_text.append(f"\n🎯 OPTIMAL BALANCE RATIOS BY CONTEXT:")
            self.results_text.append("-" * 60)

            optimal_ratios = overall_summary.get('optimal_ratios_by_context', {})
            if optimal_ratios:
                for context, ratio_info in optimal_ratios.items():
                    hist_ratio = ratio_info['historical_ratio']
                    mom_ratio = ratio_info['momentum_ratio']
                    accuracy = ratio_info['accuracy']
                    self.results_text.append(f"   {context:25} → {hist_ratio:.1f}/{mom_ratio:.1f} (accuracy: {accuracy:.3f})")
            else:
                self.results_text.append("   No optimal ratios found (insufficient statistical significance)")

            # Detailed context analysis
            if context_results:
                self.results_text.append(f"\n🔬 DETAILED CONTEXT ANALYSIS:")
                self.results_text.append("-" * 60)

                for context_key, context_data in context_results.items():
                    optimal_balance = context_data.get('optimal_balance', {})
                    recommendation = context_data.get('recommendation', 'Unknown')

                    self.results_text.append(f"\n📍 {context_key}:")
                    self.results_text.append(f"   Best Ratio: {optimal_balance.get('historical_ratio', 0):.1f}/{optimal_balance.get('momentum_ratio', 0):.1f}")
                    self.results_text.append(f"   Accuracy: {optimal_balance.get('accuracy', 0.0):.3f}")
                    self.results_text.append(f"   95% CI: {optimal_balance.get('confidence_interval_95', (0, 0))}")
                    self.results_text.append(f"   Significant: {optimal_balance.get('is_statistically_significant', False)}")
                    self.results_text.append(f"   Sample Size: {optimal_balance.get('sample_size', 0)}")
                    self.results_text.append(f"   Recommendation: {recommendation}")

            # Recommendations
            recommendations = validation_results.get('recommendations', [])
            if recommendations:
                self.results_text.append(f"\n💡 RECOMMENDATIONS:")
                self.results_text.append("-" * 40)
                for i, rec in enumerate(recommendations, 1):
                    self.results_text.append(f"   {i}. {rec}")

            # Option to apply validated ratios
            if optimal_ratios:
                self.results_text.append(f"\n🔧 NEXT STEPS:")
                self.results_text.append("   • Review the optimal ratios above")
                self.results_text.append("   • Consider applying validated ratios to improve prediction accuracy")
                self.results_text.append("   • Use the Enhanced Learning System to apply these ratios")

            self.results_text.append("\n" + "=" * 80)
            self.results_text.append("✅ Balance validation report complete!")

        except ImportError as e:
            self.results_text.append(f"\n❌ IMPORT ERROR:")
            self.results_text.append(f"   Enhanced Learning System not available: {e}")
            self.results_text.append(f"   Please ensure enhanced_adaptive_learning_system.py is properly installed.")

        except Exception as e:
            self.results_text.append(f"\n❌ UNEXPECTED ERROR:")
            self.results_text.append(f"   {str(e)}")
            import traceback
            self.results_text.append(f"\n🔍 Debug trace:")
            self.results_text.append(traceback.format_exc())

    def export_report(self):
        """Export comprehensive report"""
        try:
            report = learning_integrator.export_comprehensive_report()
            
            # Save to file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename, _ = QFileDialog.getSaveFileName(
                self, "Save Report", 
                f"learning_system_report_{timestamp}.json",
                "JSON files (*.json)"
            )
            
            if filename:
                with open(filename, 'w') as f:
                    json.dump(report, f, indent=2, default=str)
                
                QMessageBox.information(self, "Success", f"Report exported to {filename}")
        
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error exporting report: {str(e)}")


class EnhancedLearningDashboard(QMainWindow):
    """Main dashboard for the enhanced learning system"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Enhanced Learning System Dashboard")
        self.setGeometry(100, 100, 1200, 800)
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("Enhanced Tennis Prediction Learning System")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Learning Status Tab
        self.status_widget = LearningStatusWidget()
        self.tab_widget.addTab(self.status_widget, "Learning Status")
        
        # Validation Tab
        self.validation_widget = ValidationWidget()
        self.tab_widget.addTab(self.validation_widget, "Validation & Testing")
        
        layout.addWidget(self.tab_widget)
        central_widget.setLayout(layout)


def main():
    """Run the enhanced learning dashboard"""
    app = QApplication(sys.argv)
    
    dashboard = EnhancedLearningDashboard()
    dashboard.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
