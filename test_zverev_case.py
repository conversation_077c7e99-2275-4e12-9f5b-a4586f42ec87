#!/usr/bin/env python3
"""
Test the Zverev case where winner determination was failing
"""

def test_name_matching():
    """Test the name matching logic"""
    
    def generate_player_code(player_name: str) -> str:
        """Generate a 3-letter player code from player name"""
        if not player_name:
            return ""

        # Split name into parts
        name_parts = player_name.split()

        if len(name_parts) >= 2:
            # Use first 3 letters of last name
            last_name = name_parts[-1]
            if len(last_name) >= 3:
                return last_name[:3].upper()

        # Fallback: use first 3 letters of full name (no spaces)
        clean_name = player_name.replace(" ", "")
        if len(clean_name) >= 3:
            return clean_name[:3].upper()

        return ""

    def get_player_mappings():
        """Build player code to name mappings from Players folder"""
        mappings = {}

        try:
            import os
            players_folder = "Players"

            if os.path.exists(players_folder):
                for filename in os.listdir(players_folder):
                    if filename.endswith('.txt'):
                        # Extract player name from filename (remove .txt)
                        player_name = filename[:-4]

                        # Generate player code from name
                        player_code = generate_player_code(player_name)

                        if player_code:
                            mappings[player_code] = player_name

                print(f"🔍 Loaded {len(mappings)} player mappings from Players folder")
            else:
                print("🔍 Players folder not found")

        except Exception as e:
            print(f"Error loading player mappings: {e}")

        return mappings

    def name_contains_player(winner_name: str, player_code: str) -> bool:
        """Enhanced name matching using Players database"""
        if not winner_name or not player_code:
            return False

        name_lower = winner_name.lower()

        # Get dynamic mappings from Players folder
        mappings = get_player_mappings()

        # Check if player code has a mapping
        if player_code in mappings:
            mapped_name = mappings[player_code].lower()
            # Check if any part of the mapped name is in the winner name
            name_parts = mapped_name.split()
            for part in name_parts:
                if len(part) > 2 and part in name_lower:  # Only check meaningful parts
                    return True

        return False
    
    def determine_winner_simple(score1: int, score2: int, winner_name: str, player1_code: str, player2_code: str) -> str:
        """Simplified winner determination logic"""
        
        print(f"🔍 Simple analysis: Set score {score1}-{score2}, Winner name: '{winner_name}'")

        # Step 1: Determine winner from set score and winner name
        if score1 == 1 and score2 == 0:
            # Player 1 position won, but validate with winner name
            if winner_name:
                # Check if winner name clearly indicates a specific player
                if player2_code in winner_name or name_contains_player(winner_name, player2_code):
                    winner_code = player2_code
                    print(f"🔍 Winner name '{winner_name}' indicates {player2_code}")
                elif player1_code in winner_name or name_contains_player(winner_name, player1_code):
                    winner_code = player1_code
                    print(f"🔍 Winner name '{winner_name}' indicates {player1_code}")
                else:
                    # Fallback to position
                    winner_code = player1_code
                    print(f"🔍 Winner name unclear, using position → {player1_code}")
            else:
                winner_code = player1_code
                print(f"🔍 No winner name, using position → {player1_code}")
        elif score1 == 0 and score2 == 1:
            # Player 2 position won, but validate with winner name
            if winner_name:
                if player1_code in winner_name or name_contains_player(winner_name, player1_code):
                    winner_code = player1_code
                    print(f"🔍 Winner name '{winner_name}' indicates {player1_code}")
                elif player2_code in winner_name or name_contains_player(winner_name, player2_code):
                    winner_code = player2_code
                    print(f"🔍 Winner name '{winner_name}' indicates {player2_code}")
                else:
                    winner_code = player2_code
                    print(f"🔍 Winner name unclear, using position → {player2_code}")
            else:
                winner_code = player2_code
                print(f"🔍 No winner name, using position → {player2_code}")
        else:
            # Fallback
            winner_code = player1_code
            print(f"🔍 Fallback → {player1_code}")

        print(f"🔍 Determined first game winner: {winner_code}")
        return winner_code

    print("=== Testing Zverev Winner Determination ===\n")
    
    # Test the Zverev case
    print("🎾 ZVEREV TEST:")
    print("Score: 1-0")
    print("Winner name: 'Alexander Zverev'")
    print("Player codes: MUS, ZVE")
    print("Expected: ZVE should be detected as winner")
    
    winner = determine_winner_simple(1, 0, "Alexander Zverev", "MUS", "ZVE")
    print(f"Result: {winner}")
    print(f"✅ CORRECT" if winner == "ZVE" else "❌ WRONG")
    
    print("\n" + "="*50 + "\n")

    # Test player code generation
    print("🔍 PLAYER CODE GENERATION TESTS:")

    test_names = [
        "Alexander Zverev",
        "Carlos Alcaraz",
        "Lorenzo Musetti",
        "Jannik Sinner",
        "Tommy Paul",
        "Dax Donders",
        "Casper Ruud",
        "Hubert Hurkacz"
    ]

    for name in test_names:
        code = generate_player_code(name)
        print(f"'{name}' → '{code}'")

    print("\n" + "="*50 + "\n")

    # Test name matching directly
    print("🔍 DIRECT NAME MATCHING TESTS:")

    test_cases = [
        ("Alexander Zverev", "ZVE", True),
        ("Alexander Zverev", "MUS", False),
        ("Carlos Alcaraz", "ALC", True),
        ("Lorenzo Musetti", "MUS", True),
        ("Jannik Sinner", "SIN", True),
        ("Tommy Paul", "PAU", True),
    ]

    for name, code, expected in test_cases:
        result = name_contains_player(name, code)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{name}' + '{code}' → {result} (expected {expected})")

if __name__ == "__main__":
    test_name_matching()
