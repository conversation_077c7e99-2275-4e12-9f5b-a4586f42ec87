#!/usr/bin/env python3
"""
Clear the cooldown for unified validation testing
"""

def clear_cooldown():
    """Clear the cooldown for testing purposes"""
    try:
        from enhanced_adaptive_learning_system import get_global_coordinator
        
        print("🧪 Clearing Unified Validation Cooldown...")
        
        coordinator = get_global_coordinator()
        
        # Show current operations
        recent_ops = coordinator.operation_history[-3:] if coordinator.operation_history else []
        if recent_ops:
            print("📋 Recent Operations:")
            for op in recent_ops:
                print(f"   • {op.operation_type} at {op.requested_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Clear operation history temporarily
        original_count = len(coordinator.operation_history)
        coordinator.operation_history = []
        
        print(f"\n✅ Cleared {original_count} operations from history")
        print("⚠️ This is for testing purposes only!")
        print("💡 You can now run unified validation through the dashboard")
        
        return True
        
    except Exception as e:
        print(f"❌ Error clearing cooldown: {e}")
        return False

if __name__ == "__main__":
    clear_cooldown()
