#!/usr/bin/env python3
"""
Debug script to understand why validation filtering is removing all predictions
"""

def debug_validation_filtering():
    """Debug the validation filtering process"""
    print('🔍 DEBUGGING VALIDATION FILTERING')
    print('=' * 50)

    try:
        from learning_system_integration import learning_integrator
        from robust_validation_system import robust_validator
        
        # Get all predictions
        all_predictions = learning_integrator.learning_system.prediction_tracker.predictions
        ai_predictions = [p for p in all_predictions if getattr(p, 'is_ai_prediction', False)]
        
        print(f'Total predictions: {len(all_predictions)}')
        print(f'AI predictions: {len(ai_predictions)}')
        
        # Test validation filtering
        filtered_predictions = robust_validator._filter_predictions(ai_predictions)
        print(f'Validation-filtered predictions: {len(filtered_predictions)}')
        
        # Analyze what's being filtered out
        print('\n🔍 FILTERING ANALYSIS:')
        
        filter_stats = {
            'not_ai': 0,
            'no_actual_winner': 0,
            'no_prompt_weights': 0,
            'no_surface': 0,
            'no_tournament_level': 0,
            'incomplete_data': 0,
            'passed': 0
        }
        
        for i, pred in enumerate(ai_predictions):
            if i < 3:  # Show details for first 3
                print(f'\nPrediction {i+1} Details:')
                print(f'  is_ai_prediction: {getattr(pred, "is_ai_prediction", "Missing")}')
                print(f'  actual_winner: {getattr(pred, "actual_winner", "Missing")}')
                print(f'  prompt_weights: {"Present" if getattr(pred, "prompt_weights", None) else "Missing"}')
                print(f'  surface: {getattr(pred, "surface", "Missing")}')
                print(f'  tournament_level: {"Present" if hasattr(pred, "tournament_level") else "Missing"}')
                print(f'  player1_name: {getattr(pred, "player1_name", "Missing")}')
                print(f'  predicted_winner: {getattr(pred, "predicted_winner", "Missing")}')
            
            # Check filtering reasons
            if not getattr(pred, 'is_ai_prediction', False):
                filter_stats['not_ai'] += 1
                continue
            if getattr(pred, 'actual_winner', None) is None:
                filter_stats['no_actual_winner'] += 1
                continue
            if getattr(pred, 'prompt_weights', None) is None:
                filter_stats['no_prompt_weights'] += 1
                continue
            if not getattr(pred, 'surface', None):
                filter_stats['no_surface'] += 1
                continue
            if not hasattr(pred, 'tournament_level'):
                filter_stats['no_tournament_level'] += 1
                continue
            
            # Check complete data requirement
            if robust_validator.config.require_complete_data:
                if not all([pred.player1_name, pred.player2_name, pred.predicted_winner]):
                    filter_stats['incomplete_data'] += 1
                    continue
            
            filter_stats['passed'] += 1
        
        print('\n📊 FILTERING STATISTICS:')
        for reason, count in filter_stats.items():
            if count > 0:
                print(f'  {reason}: {count} predictions')
        
        # Check validation config
        print(f'\n⚙️ VALIDATION CONFIG:')
        print(f'  require_complete_data: {robust_validator.config.require_complete_data}')
        print(f'  min_train_size: {robust_validator.config.min_train_size}')
        print(f'  min_test_size: {robust_validator.config.min_test_size}')
        print(f'  Required minimum: {robust_validator.config.min_train_size + robust_validator.config.min_test_size}')
        
        return filter_stats
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
        return None

def suggest_fixes(filter_stats):
    """Suggest fixes based on filtering results"""
    if not filter_stats:
        return
    
    print('\n🔧 SUGGESTED FIXES:')
    
    if filter_stats['no_actual_winner'] > 0:
        print(f'❌ {filter_stats["no_actual_winner"]} predictions missing actual winners')
        print('   Fix: Record match outcomes using "Player 1 Won"/"Player 2 Won" buttons')
    
    if filter_stats['no_prompt_weights'] > 0:
        print(f'❌ {filter_stats["no_prompt_weights"]} predictions missing prompt weights')
        print('   Fix: These may be old predictions - use newer AI predictions')
    
    if filter_stats['no_tournament_level'] > 0:
        print(f'❌ {filter_stats["no_tournament_level"]} predictions missing tournament level')
        print('   Fix: Ensure tournament level is set when making predictions')
    
    if filter_stats['no_surface'] > 0:
        print(f'❌ {filter_stats["no_surface"]} predictions missing surface info')
        print('   Fix: Ensure surface (Clay/Hard/Grass) is set in input data')
    
    if filter_stats['passed'] > 0:
        print(f'✅ {filter_stats["passed"]} predictions passed all filters')
        if filter_stats['passed'] >= 50:
            print('   Status: Should be enough for validation!')
        else:
            print(f'   Status: Need {50 - filter_stats["passed"]} more valid predictions')

if __name__ == "__main__":
    stats = debug_validation_filtering()
    suggest_fixes(stats)
