#!/usr/bin/env python3
"""
Test script to run balance validation with the fixed simulation logic
"""

from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem

def main():
    print("Running Balance Validation with Fixed Simulation Logic...")
    print("=" * 60)

    # Create enhanced learning system instance
    enhanced_system = EnhancedAdaptiveLearningSystem()

    # Run the robust balance validation
    results = enhanced_system.run_robust_balance_validation()

    print("\n" + "=" * 60)
    print("VALIDATION COMPLETE!")
    print("=" * 60)
    
    # Print key results
    if results.get('status') == 'success':
        overall_summary = results.get('overall_summary', {})
        print(f"\nOverall Accuracy: {overall_summary.get('overall_accuracy', 'N/A'):.3f}")
        print(f"Best Balance Ratio: {overall_summary.get('best_balance_ratio', 'N/A')}")
        print(f"Statistical Significance: {overall_summary.get('statistical_significance', 'N/A')}")
        
        context_results = results.get('context_results', {})
        print(f"\nContexts Tested: {len(context_results)}")
        
        for context, data in context_results.items():
            accuracy = data.get('best_accuracy', 0)
            ratio = data.get('best_ratio', 'N/A')
            print(f"  {context}: {accuracy:.3f} accuracy with {ratio}")
            
    elif results.get('status') == 'insufficient_data':
        print(f"\nInsufficient data: {results.get('message', 'Unknown')}")
        print(f"Completed predictions: {results.get('completed_predictions', 0)}")
        print(f"Total predictions: {results.get('total_predictions', 0)}")
    else:
        print(f"\nValidation failed: {results.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
