#!/usr/bin/env python3
"""
Test pure score-based detection without hardcoded names
"""

def test_score_based_detection():
    """Test score-based detection for various scenarios"""
    
    test_cases = [
        {
            "name": "<PERSON> (Player 2 wins)",
            "data": """0
-
0
tied
SIN
PAU
15
0
15
15
15
30
15
40
1
-
0
<PERSON>""",
            "player1": "SIN",
            "player2": "PAU", 
            "expected": "PAU",
            "reason": "Score progression: 15-0, 15-15, 15-30, 15-40 → Player 2 (PAU) wins"
        },
        {
            "name": "Player 1 wins with 40-0",
            "data": """0
-
0
tied
KHA
ALC
15
0
30
0
40
0
1
-
0
<PERSON>""",
            "player1": "KHA",
            "player2": "ALC",
            "expected": "KHA", 
            "reason": "Score progression: 15-0, 30-0, 40-0 → Player 1 (KHA) wins"
        },
        {
            "name": "Player 2 wins with deuce game",
            "data": """0
-
0
tied
DJO
NAD
15
0
15
15
15
30
30
30
30
40
40
40
40
AD
1
-
0
<PERSON>""",
            "player1": "DJO",
            "player2": "NAD",
            "expected": "NAD",
            "reason": "Deuce game ending with AD → Player 2 (NAD) wins"
        }
    ]
    
    print("🧪 Pure Score-Based Detection Test")
    print("=" * 50)
    print("Testing WITHOUT hardcoded player names")
    print("Using ONLY score progression analysis")
    print()
    
    all_pass = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📋 TEST {i}: {test_case['name']}")
        print(f"Expected: {test_case['expected']} - {test_case['reason']}")
        
        result = analyze_score_based(
            test_case['data'], 
            test_case['player1'], 
            test_case['player2']
        )
        
        success = result.get('starting_server') == test_case['expected']
        status = "✅ PASS" if success else "❌ FAIL"
        
        print(f"Result: {status} - {result.get('starting_server')} ({result.get('logic', 'No logic')})")
        
        if not success:
            all_pass = False
            print(f"   Expected: {test_case['expected']}, Got: {result.get('starting_server')}")
        
        print()
    
    print("=" * 50)
    print(f"🏆 OVERALL RESULT: {'✅ ALL TESTS PASS' if all_pass else '❌ SOME TESTS FAILED'}")
    
    if all_pass:
        print("\n🎾 Pure score-based detection is working perfectly!")
        print("   No hardcoded names needed! 🚀")

def analyze_score_based(match_data: str, player1_code: str, player2_code: str) -> dict:
    """Analyze using pure score-based detection"""
    try:
        lines = match_data.strip().split('\n')
        first_game_data = extract_score_based(lines, player1_code, player2_code)
        
        if not first_game_data:
            return {"error": "No first game found"}
        
        first_game_winner = first_game_data['winner']
        has_break_point = first_game_data['has_bp']
        
        # Apply Break Point Theory
        if has_break_point:
            if first_game_winner == player1_code:
                starting_server = player2_code
                logic = f"BP detected. {player1_code} broke {player2_code}'s serve → {player2_code} started serving"
            else:
                starting_server = player1_code
                logic = f"BP detected. {player2_code} broke {player1_code}'s serve → {player1_code} started serving"
        else:
            starting_server = first_game_winner
            logic = f"No BP. {first_game_winner} held serve → {first_game_winner} started serving"
        
        return {
            "starting_server": starting_server,
            "logic": logic,
            "first_game_winner": first_game_winner,
            "has_bp": has_break_point
        }
        
    except Exception as e:
        return {"error": str(e)}

def extract_score_based(lines: list, player1_code: str, player2_code: str) -> dict:
    """Extract using pure score-based analysis"""
    try:
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            if not line:
                i += 1
                continue
            
            # Look for first game pattern
            if (i + 2 < len(lines) and 
                lines[i+1].strip() == '-' and 
                lines[i].strip().isdigit() and 
                lines[i+2].strip().isdigit()):
                
                score1 = int(lines[i].strip())
                score2 = int(lines[i+2].strip())
                
                if (score1 == 1 and score2 == 0) or (score1 == 0 and score2 == 1):
                    i += 3
                    
                    # Skip winner name (we don't use it)
                    if i < len(lines) and any(char.isalpha() for char in lines[i]) and ' ' in lines[i]:
                        i += 1
                    
                    # Extract game data
                    game_data_lines = []
                    game_start = i - 4 if i > 3 else 0
                    while game_start >= 0:
                        if lines[game_start].strip().lower() == 'tied':
                            break
                        game_start -= 1
                    
                    if game_start >= 0:
                        score_start = i - 4 if i > 3 else i - 3
                        for j in range(game_start + 1, score_start):
                            if j < len(lines):
                                line = lines[j].strip()
                                if line and not (len(line) == 3 and line.isupper()):
                                    game_data_lines.append(line)
                    
                    # Determine winner using PURE score analysis
                    winner_code = determine_winner_from_scores(game_data_lines, score1, score2, player1_code, player2_code)
                    
                    # Check for BP
                    has_bp = any('BP' in game_line for game_line in game_data_lines)
                    
                    print(f"🔍 Game data: {game_data_lines}")
                    print(f"🔍 Determined winner: {winner_code}")
                    
                    return {
                        'winner': winner_code,
                        'has_bp': has_bp,
                        'game_data': game_data_lines
                    }
            
            i += 1
        
        return None
        
    except Exception as e:
        return None

def determine_winner_from_scores(game_data_lines: list, score1: int, score2: int, player1_code: str, player2_code: str) -> str:
    """Determine winner purely from score progression"""
    try:
        if not game_data_lines:
            # Fallback to set score
            return player1_code if score1 == 1 else player2_code
        
        # Find tennis scores in the data
        tennis_scores = []
        for line in game_data_lines:
            if line in ['0', '15', '30', '40', 'AD']:
                tennis_scores.append(line)
        
        print(f"🔍 Tennis scores found: {tennis_scores}")
        
        if len(tennis_scores) >= 2:
            # Analyze the final scores
            # In the data format, scores alternate: P1, P2, P1, P2, ...
            # So even indices (0,2,4...) are Player 1, odd indices (1,3,5...) are Player 2
            
            last_score = tennis_scores[-1]
            second_last_score = tennis_scores[-2]
            
            print(f"🔍 Final scores: {second_last_score} vs {last_score}")
            
            # Determine who won based on final score
            if last_score == '40' and second_last_score in ['0', '15', '30']:
                # Last player (odd/even position) won with 40
                winner_is_player2 = (len(tennis_scores) % 2 == 0)
                return player2_code if winner_is_player2 else player1_code
            elif second_last_score == '40' and last_score in ['0', '15', '30']:
                # Second-to-last player won with 40
                winner_is_player1 = (len(tennis_scores) % 2 == 1)
                return player1_code if winner_is_player1 else player2_code
            elif last_score == 'AD':
                # Last player won with advantage
                winner_is_player2 = (len(tennis_scores) % 2 == 0)
                return player2_code if winner_is_player2 else player1_code
            elif second_last_score == 'AD':
                # Second-to-last player won with advantage
                winner_is_player1 = (len(tennis_scores) % 2 == 1)
                return player1_code if winner_is_player1 else player2_code
        
        # Fallback to set score
        print(f"🔍 Fallback to set score: {score1}-{score2}")
        return player1_code if score1 == 1 else player2_code
        
    except Exception as e:
        print(f"Error in score analysis: {e}")
        return player1_code

if __name__ == "__main__":
    test_score_based_detection()
