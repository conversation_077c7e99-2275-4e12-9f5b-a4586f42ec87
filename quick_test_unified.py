#!/usr/bin/env python3
"""
Quick test to verify the unified validation system fix
"""

def test_import():
    """Test if the unified validation system can be imported without errors"""
    try:
        print("🧪 Testing unified validation system import...")
        
        from enhanced_adaptive_learning_system import (
            run_unified_validation, 
            apply_unified_optimization,
            get_coordination_status,
            enhanced_learning_system,
            UnifiedValidationSystem,
            LearningSystemCoordinator
        )
        
        print("✅ All imports successful!")
        
        # Test creating instances
        print("🔧 Testing instance creation...")
        
        unified_validator = UnifiedValidationSystem()
        print("✅ UnifiedValidationSystem created successfully!")
        
        coordinator = LearningSystemCoordinator()
        print("✅ LearningSystemCoordinator created successfully!")
        
        # Test coordination status
        print("📊 Testing coordination status...")
        status = get_coordination_status()
        print(f"✅ Coordination status retrieved: {status.get('current_state', 'Unknown')}")
        
        # Test with minimal data
        print("📈 Testing with current prediction data...")
        learning_predictions = enhanced_learning_system.get_learning_eligible_predictions()
        print(f"✅ Found {len(learning_predictions)} learning-eligible predictions")
        
        if len(learning_predictions) >= 5:  # Very minimal test
            print("🔬 Testing unified validation with minimal data...")
            
            # Temporarily lower requirements
            if hasattr(enhanced_learning_system.coordinator, 'unified_validator'):
                original_min = enhanced_learning_system.coordinator.unified_validator.min_sample_size if hasattr(enhanced_learning_system.coordinator.unified_validator, 'min_sample_size') else 150
                enhanced_learning_system.coordinator.unified_validator.min_sample_size = 5
                print(f"   Lowered minimum sample size from {original_min} to 5")
            
            try:
                # Test the function call that was failing
                validation_results = run_unified_validation()
                print(f"✅ Unified validation completed: {validation_results.get('status', 'Unknown')}")
                
                if validation_results.get('status') == 'insufficient_data':
                    print(f"   ℹ️ Insufficient data (expected): {validation_results.get('sample_size', 0)} predictions")
                elif validation_results.get('status') == 'success':
                    print("   🎉 Validation successful!")
                elif validation_results.get('status') == 'blocked':
                    print(f"   ⏸️ Validation blocked: {validation_results.get('message', 'Unknown reason')}")
                else:
                    print(f"   ⚠️ Other status: {validation_results.get('status', 'Unknown')}")
                
            except Exception as e:
                print(f"❌ Error during unified validation test: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            finally:
                # Restore original requirements
                if hasattr(enhanced_learning_system.coordinator, 'unified_validator'):
                    enhanced_learning_system.coordinator.unified_validator.min_sample_size = original_min
                    print(f"   Restored minimum sample size to {original_min}")
        else:
            print(f"⏸️ Skipping validation test - need at least 5 predictions (have {len(learning_predictions)})")
        
        print("\n🎉 All tests passed! The unified validation system is working correctly.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🔬 Quick Test: Unified Validation System")
    print("=" * 50)
    
    success = test_import()
    
    if success:
        print("\n✅ The LearningSystemOperation error has been fixed!")
        print("💡 You can now use the unified validation system through:")
        print("   • Enhanced Learning Dashboard (purple button)")
        print("   • test_unified_validation.py script")
        print("   • Direct function calls")
    else:
        print("\n❌ There are still issues with the unified validation system")
        print("💡 Please check the error messages above")

if __name__ == "__main__":
    main()
