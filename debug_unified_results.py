#!/usr/bin/env python3
"""
Debug script to see the detailed unified validation results
"""

import json
from enhanced_adaptive_learning_system import test_unified_validation_with_bypass

def debug_unified_results():
    """Debug the unified validation results to see what's actually returned"""
    print("🔍 Debugging Unified Validation Results")
    print("=" * 60)
    
    try:
        # Run unified validation
        print("🔬 Running unified validation...")
        results = test_unified_validation_with_bypass()
        
        print(f"\n📊 Raw Results Structure:")
        print(f"   Status: {results.get('status', 'Unknown')}")
        print(f"   Keys: {list(results.keys())}")
        
        # Show overall summary
        overall_summary = results.get('overall_summary', {})
        if overall_summary:
            print(f"\n📈 Overall Summary:")
            for key, value in overall_summary.items():
                print(f"   {key}: {value}")
        
        # Show context results
        context_results = results.get('context_results', {})
        if context_results:
            print(f"\n🎯 Context Results:")
            for context, data in context_results.items():
                print(f"\n   {context}:")
                if isinstance(data, dict):
                    for key, value in data.items():
                        if key == 'best_combination':
                            print(f"     {key}:")
                            if isinstance(value, dict):
                                for combo_key, combo_value in value.items():
                                    print(f"       {combo_key}: {combo_value}")
                        else:
                            print(f"     {key}: {value}")
        
        # Show optimal configurations
        optimal_configs = results.get('optimal_configurations', {})
        if optimal_configs:
            print(f"\n⚙️ Optimal Configurations:")
            for context, config in optimal_configs.items():
                print(f"\n   {context}:")
                for key, value in config.items():
                    print(f"     {key}: {value}")
        
        # Show recommendations
        recommendations = results.get('recommendations', [])
        if recommendations:
            print(f"\n💡 Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        
        # Save detailed results to file for inspection
        with open('debug_unified_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n💾 Detailed results saved to debug_unified_results.json")
        
        return results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    debug_unified_results()
