{"validation_timestamp": "2025-07-29T19:41:28.163684", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6734696190476189, "confidence_interval_95": [0.6530612244897959, 0.6938775510204082], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.050906903221414175, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6054421768707483, "std_accuracy": 0.06937441515092223, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.6054421768707483, "std_accuracy": 0.06937441515092223, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5102040816326531, "std_accuracy": 0.016663195529137285, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.673469387755102, "std_accuracy": 0.016663195529137285, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5986394557823129, "std_accuracy": 0.03848200169722707, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.044086671417740544, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.673) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6666566046511627, "confidence_interval_95": [0.5813953488372093, 0.7441860465116279], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5503875968992248, "std_accuracy": 0.047786155061775006, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.03288868749704873, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5348837209302326, "std_accuracy": 0.07595317031885823, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5968992248062015, "std_accuracy": 0.029005096021503425, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5503875968992249, "std_accuracy": 0.029005096021503425, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6589147286821705, "std_accuracy": 0.047786155061775, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6666666666666666, "std_accuracy": 0.06668469199257848, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.667) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5930571564173843, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.6, "momentum_ratio": 0.4, "accuracy": 0.6734696190476189}, "Clay_Set2_Mid": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6666566046511627}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.6/0.4 (accuracy: 0.673)", "  • Clay_Set2_Mid: 0.8/0.2 (accuracy: 0.667)"]}