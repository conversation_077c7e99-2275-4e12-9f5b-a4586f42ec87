"""
EWMA (Exponentially Weighted Moving Average) Dynamic Weight System
This module implements adaptive weight learning based on prediction accuracy
"""

import json
import os
from typing import Dict, <PERSON><PERSON>, Optional
from dataclasses import dataclass, field, asdict
from datetime import datetime
import math

@dataclass
class WeightContext:
    """Context for weight adjustments"""
    score: Tuple[int, int]
    set_number: int
    is_tiebreak: bool
    serving_player: str
    
    def get_score_category(self) -> str:
        """Categorize score into strategic situations"""
        s1, s2 = self.score
        
        # Tiebreak
        if self.is_tiebreak:
            return "tiebreak"
        
        # Critical tied scores
        if s1 == s2:
            if s1 >= 5:
                return "deuce_critical"  # 5-5, 6-6
            elif s1 >= 3:
                return "tied_mid"  # 3-3, 4-4
            else:
                return "tied_early"  # 0-0, 1-1, 2-2
        
        # Leading situations
        diff = abs(s1 - s2)
        leader = "server" if (s1 > s2 and self.serving_player == "p1") or (s2 > s1 and self.serving_player == "p2") else "returner"
        
        if diff >= 2:
            return f"{leader}_commanding"  # 4-1, 5-2, etc
        else:
            return f"{leader}_slight"  # 3-2, 4-3, etc

@dataclass
class EWMAWeights:
    """Dynamic weights using EWMA for different prediction contexts"""
    # Rebalanced momentum weights - capped at ±0.15 to prevent overreaction
    dominant_serving: float = 0.15   # Reduced from 0.20
    strong_serving: float = 0.12     # Reduced from 0.15
    solid_serving: float = 0.08
    shaky_serving: float = -0.06     # Unchanged
    weak_serving: float = -0.10      # Reduced from -0.12
    vulnerable_serving: float = -0.15 # Reduced from -0.20
    break_point_pressure: float = -0.12 # Unchanged
    clutch_serving: float = 0.12
    momentum_shift: float = -0.15    # Reduced from -0.18
    comeback_mode: float = 0.10
    choking: float = -0.15           # Reduced from -0.25 to prevent overreaction
    fatigue_showing: float = -0.08   # Unchanged
    pressure_handling: float = 0.15
    rhythm_finding: float = 0.08
    
    # Pattern weights - capped at ±0.15 to prevent overreaction
    consecutive_015: float = -0.15  # Reduced from -0.18
    three_point_runs: float = 0.12  # Unchanged
    recent_break_points: float = -0.10  # Unchanged
    
    # Enhanced momentum pattern weights - capped at ±0.15
    comeback_from_0_40: float = 0.15
    choke_from_40_0: float = -0.15    # Reduced from -0.20
    love_hold: float = 0.08
    love_break: float = -0.12
    deuce_battle_won: float = 0.05
    deuce_battle_lost: float = -0.05
    high_importance_hold: float = 0.10
    high_importance_break: float = -0.15
    
    # Score situation modifiers
    score_modifiers: Dict[str, float] = field(default_factory=lambda: {
        "tiebreak": 0.0,
        "deuce_critical": -0.05,  # More pressure at 5-5, 6-6
        "tied_mid": -0.03,        # Moderate pressure at 3-3, 4-4
        "tied_early": 0.0,        # No extra pressure early
        "server_commanding": 0.05,  # Server with big lead
        "server_slight": 0.02,      # Server with small lead
        "returner_commanding": -0.08,  # Server trailing badly
        "returner_slight": -0.04,      # Server trailing slightly
    })
    
    # Set-specific modifiers
    set_modifiers: Dict[int, float] = field(default_factory=lambda: {
        1: 0.0,    # First set baseline
        2: -0.02,  # Second set slightly more pressure
        3: -0.05,  # Third set deciding pressure
        4: -0.03,  # Fourth set
        5: -0.08,  # Fifth set maximum pressure
    })
    
    # Score-Set combination modifiers (e.g., "3-3_1" for 3-3 in set 1)
    score_set_modifiers: Dict[str, float] = field(default_factory=dict)
    
    # Track performance by score-set combination for learning
    score_set_performance: Dict[str, Dict[str, int]] = field(default_factory=lambda: {})
    
    # Player-specific break probabilities (will be calculated dynamically)
    player_break_rates: Dict[str, float] = field(default_factory=dict)
    
    # EWMA parameters
    alpha: float = 0.25  # Learning rate (0.25 = faster adaptation for better responsiveness)
    prediction_count: int = 0
    accuracy_history: list = field(default_factory=list)
    
    def update_weights(self, prediction_correct: bool, context: WeightContext, 
                      momentum_type: str, pattern_flags: Dict[str, bool]):
        """Update weights based on prediction outcome using EWMA"""
        self.prediction_count += 1
        
        # Calculate prediction error
        error = 1.0 if prediction_correct else -1.0
        
        # Update momentum weight that was used
        momentum_weight_map = {
            'STRONG_SERVING': 'strong_serving',
            'WEAK_SERVING': 'weak_serving',
            'BREAK_POINT_PRESSURE': 'break_point_pressure',
            'MOMENTUM_SHIFT': 'momentum_shift',
        }
        
        # Define bounds for each momentum type - improved bounds to prevent boundary convergence
        momentum_bounds = {
            'DOMINANT_SERVING': (0.15, 0.35),       # Very positive - dominant holds
            'STRONG_SERVING': (0.05, 0.25),         # Positive - helps holds
            'SOLID_SERVING': (0.02, 0.15),          # Slightly positive - steady serving
            'SHAKY_SERVING': (-0.1, 0.02),          # Slightly negative - some difficulty
            'WEAK_SERVING': (-0.25, -0.05),         # Negative - hurts holds
            'VULNERABLE_SERVING': (-0.4, -0.15),    # Very negative - vulnerable
            'BREAK_POINT_PRESSURE': (-0.25, -0.02), # Negative - pressure situations
            'CLUTCH_SERVING': (0.05, 0.2),          # Positive - clutch performance
            'MOMENTUM_SHIFT': (-0.35, -0.08),       # Negative - expect breaks
            'COMEBACK_MODE': (0.02, 0.2),           # Positive - comeback ability
            'CHOKING': (-0.45, -0.15),              # Very negative - choking
            'FATIGUE_SHOWING': (-0.15, -0.02),      # Negative - fatigue impact
            'PRESSURE_HANDLING': (0.05, 0.25),      # Positive - handles pressure
            'RHYTHM_FINDING': (0.02, 0.15),         # Positive - improving rhythm
        }
        
        if momentum_type in momentum_weight_map:
            attr = momentum_weight_map[momentum_type]
            current = getattr(self, attr)
            # EWMA update: new = alpha * error_adjustment + (1 - alpha) * current
            # Increased adjustment size for faster learning
            adjustment = error * 0.04  # Larger adjustment per prediction for faster adaptation
            new_value = self.alpha * adjustment + (1 - self.alpha) * current
            # Constrain weights to logical bounds for each type
            min_val, max_val = momentum_bounds.get(momentum_type, (-0.5, 0.3))
            new_value = max(min_val, min(max_val, new_value))
            setattr(self, attr, new_value)
        
        # Update pattern weights that were active
        if pattern_flags.get('consecutive_015', False):
            self.consecutive_015 = self._update_single_weight(self.consecutive_015, error, -0.4, -0.1)  # Increased penalty range
        
        if pattern_flags.get('three_point_runs', False):
            self.three_point_runs = self._update_single_weight(self.three_point_runs, error, 0.08, 0.30)  # Increased bonus range
        
        if pattern_flags.get('recent_break_points', False):
            self.recent_break_points = self._update_single_weight(self.recent_break_points, error, -0.2, -0.05)
        
        # Update score modifier
        score_cat = context.get_score_category()
        if score_cat in self.score_modifiers:
            current = self.score_modifiers[score_cat]
            adjustment = error * 0.01  # Smaller adjustment for modifiers
            new_value = self.alpha * adjustment + (1 - self.alpha) * current
            self.score_modifiers[score_cat] = max(-0.15, min(0.15, new_value))
        
        # Update score-set combination modifier
        score_set_key = f"{context.score[0]}-{context.score[1]}_{context.set_number}"
        
        # Initialize if not exists
        if score_set_key not in self.score_set_modifiers:
            self.score_set_modifiers[score_set_key] = 0.0
            self.score_set_performance[score_set_key] = {'total': 0, 'correct': 0}
        
        # Track performance
        self.score_set_performance[score_set_key]['total'] += 1
        if prediction_correct:
            self.score_set_performance[score_set_key]['correct'] += 1
        
        # Update score-set modifier with EWMA
        current_ss = self.score_set_modifiers[score_set_key]
        adjustment_ss = error * 0.015  # Moderate adjustment for score-set combos
        new_value_ss = self.alpha * adjustment_ss + (1 - self.alpha) * current_ss
        self.score_set_modifiers[score_set_key] = max(-0.2, min(0.2, new_value_ss))
        
        # Track accuracy
        self.accuracy_history.append({
            'timestamp': datetime.now().isoformat(),
            'correct': prediction_correct,
            'context': asdict(context),
            'momentum': momentum_type
        })
        
        # Keep only recent history (last 100 predictions)
        if len(self.accuracy_history) > 100:
            self.accuracy_history = self.accuracy_history[-100:]
    
    def _update_single_weight(self, current: float, error: float, min_val: float, max_val: float) -> float:
        """Update a single weight using EWMA with bounds"""
        # Increased adjustment for faster learning
        adjustment = error * 0.04  # Doubled from 0.02 for faster adaptation
        new_value = self.alpha * adjustment + (1 - self.alpha) * current
        return max(min_val, min(max_val, new_value))
    
    def get_total_adjustment(self, context: WeightContext, momentum_type: str, 
                           pattern_flags: Dict[str, bool]) -> float:
        """Calculate total adjustment based on all factors"""
        adjustment = 0.0
        
        # Enhanced momentum adjustment with granular categories
        momentum_map = {
            'DOMINANT_SERVING': self.dominant_serving,
            'STRONG_SERVING': self.strong_serving,
            'SOLID_SERVING': self.solid_serving,
            'SHAKY_SERVING': self.shaky_serving,
            'WEAK_SERVING': self.weak_serving,
            'VULNERABLE_SERVING': self.vulnerable_serving,
            'BREAK_POINT_PRESSURE': self.break_point_pressure,
            'CLUTCH_SERVING': self.clutch_serving,
            'MOMENTUM_SHIFT': self.momentum_shift,
            'COMEBACK_MODE': self.comeback_mode,
            'CHOKING': self.choking,
            'FATIGUE_SHOWING': self.fatigue_showing,
            'PRESSURE_HANDLING': self.pressure_handling,
            'RHYTHM_FINDING': self.rhythm_finding,
        }
        adjustment += momentum_map.get(momentum_type, 0.0)
        
        # Pattern adjustments
        if pattern_flags.get('consecutive_015', False):
            adjustment += self.consecutive_015
        if pattern_flags.get('three_point_runs', False):
            adjustment += self.three_point_runs
        if pattern_flags.get('recent_break_points', False):
            adjustment += self.recent_break_points
        
        # Score situation modifier
        score_cat = context.get_score_category()
        adjustment += self.score_modifiers.get(score_cat, 0.0)
        
        # Set modifier
        adjustment += self.set_modifiers.get(context.set_number, 0.0)
        
        # Score-Set combination modifier
        score_set_key = f"{context.score[0]}-{context.score[1]}_{context.set_number}"
        adjustment += self.score_set_modifiers.get(score_set_key, 0.0)
        
        # Break probability differential (if available)
        if len(self.player_break_rates) >= 2:
            # Adjust based on relative break strength
            server_break_rate = self.player_break_rates.get(context.serving_player, 0.2)
            opponent_break_rate = list(self.player_break_rates.values())[0] if context.serving_player == list(self.player_break_rates.keys())[1] else list(self.player_break_rates.values())[1]
            
            # If opponent breaks more often, server is under more pressure
            break_differential = opponent_break_rate - server_break_rate
            adjustment += break_differential * 0.15  # Scale the impact
        
        return adjustment
    
    def calculate_confidence_adjustment(self) -> float:
        """Calculate confidence adjustment based on recent accuracy"""
        if len(self.accuracy_history) < 5:
            return 1.0  # No adjustment with limited data
        
        # Calculate recent accuracy (last 20 predictions)
        recent = self.accuracy_history[-20:]
        accuracy = sum(1 for p in recent if p['correct']) / len(recent)
        
        # Sigmoid function to map accuracy to confidence multiplier
        # 50% accuracy = 1.0 multiplier, 70% = 1.2x, 30% = 0.8x
        confidence_mult = 1.0 + 0.4 * (accuracy - 0.5)
        return max(0.5, min(1.5, confidence_mult))
    
    def save_weights(self, filepath: str = "ewma_weights.json"):
        """Save current weights to file"""
        data = {
            'weights': asdict(self),
            'timestamp': datetime.now().isoformat(),
            'prediction_count': self.prediction_count,
            'recent_accuracy': self._calculate_recent_accuracy()
        }
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_weights(self, filepath: str = "ewma_weights.json") -> bool:
        """Load weights from file"""
        if not os.path.exists(filepath):
            return False
        
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            weights = data.get('weights', {})
            for key, value in weights.items():
                if hasattr(self, key) and not key.startswith('_'):
                    setattr(self, key, value)
            
            return True
        except Exception:
            return False
    
    def _calculate_recent_accuracy(self) -> float:
        """Calculate accuracy over recent predictions"""
        if not self.accuracy_history:
            return 0.0
        
        recent = self.accuracy_history[-20:]  # Last 20 predictions
        return sum(1 for p in recent if p['correct']) / len(recent)
    
    def update_break_probabilities(self, player_stats: Dict[str, Dict[str, int]]):
        """Update player-specific break probabilities"""
        for player, stats in player_stats.items():
            games_on_return = stats.get('return_games', 0)
            breaks_achieved = stats.get('breaks_won', 0)
            
            if games_on_return > 0:
                self.player_break_rates[player] = breaks_achieved / games_on_return
            else:
                self.player_break_rates[player] = 0.2  # Default 20% break rate
    
    def learn_from_historical_predictions(self, predictions: list):
        """
        Learn score-set patterns from historical prediction data.
        This allows the model to immediately benefit from past predictions.
        """
        if not predictions:
            return
        
        # Silently learn from historical predictions (removed verbose logging)
        # print(f"Learning from {len(predictions)} historical predictions...")

        # Reset score-set data for fresh learning
        self.score_set_modifiers = {}
        self.score_set_performance = {}
        
        # Analyze each prediction
        for pred in predictions:
            # Skip if no outcome recorded
            if not hasattr(pred, 'actual_winner') or pred.actual_winner is None:
                continue
            
            # Extract score and set
            if hasattr(pred, 'score') and hasattr(pred, 'set_number'):
                if isinstance(pred.score, tuple):
                    score = pred.score
                elif isinstance(pred.score, str):
                    # Parse "3-3" format
                    parts = pred.score.split('-')
                    if len(parts) == 2:
                        score = (int(parts[0]), int(parts[1]))
                    else:
                        continue
                else:
                    continue
                
                score_set_key = f"{score[0]}-{score[1]}_{pred.set_number}"
                
                # Initialize if not exists
                if score_set_key not in self.score_set_performance:
                    self.score_set_performance[score_set_key] = {'total': 0, 'correct': 0}
                    self.score_set_modifiers[score_set_key] = 0.0
                
                # Track performance
                self.score_set_performance[score_set_key]['total'] += 1
                if pred.predicted_winner == pred.actual_winner:
                    self.score_set_performance[score_set_key]['correct'] += 1
        
        # Calculate initial weights based on performance deviation from baseline
        baseline_accuracy = 0.5  # 50% baseline
        
        for score_set_key, perf in self.score_set_performance.items():
            if perf['total'] >= 3:  # Minimum 3 predictions for reliability
                accuracy = perf['correct'] / perf['total']
                
                # Calculate weight based on deviation from baseline
                # Positive weight if model performs above baseline, negative if below
                deviation = accuracy - baseline_accuracy
                
                # Scale the weight (max ±15% for strong patterns)
                initial_weight = deviation * 0.3  # 30% scaling factor
                
                # Apply bounds
                self.score_set_modifiers[score_set_key] = max(-0.15, min(0.15, initial_weight))
                
                # Silently learn patterns (removed verbose logging)
                # print(f"  {score_set_key}: {perf['correct']}/{perf['total']} = {accuracy:.1%} "
                #       f"(weight: {self.score_set_modifiers[score_set_key]:+.3f})")

        # Silently complete learning (removed verbose logging)
        # print(f"Learned {len(self.score_set_modifiers)} score-set patterns")