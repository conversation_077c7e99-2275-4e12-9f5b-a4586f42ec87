# Manual Reset Guide - When Files Are Locked

## 🚨 **When to Use This Guide**

Use this manual reset when:
- ✅ Automated reset scripts fail due to "file in use" errors
- ✅ Database files are locked by running processes
- ✅ You need to ensure a completely clean reset

## 🛑 **Step 1: Close All Applications**

**Close these applications completely**:
1. ✅ **enhanced_gui.py** - Close the main tennis application
2. ✅ **enhanced_learning_dashboard.py** - Close learning dashboard
3. ✅ **Any Python IDEs** - Close PyCharm, VS Code, etc. if they have the project open
4. ✅ **Command prompts** - Close any terminals running Python scripts

**Verify closure**:
- Press `Ctrl+Shift+Esc` to open Task Manager
- Look for any `python.exe` processes
- End any processes related to your tennis application

## 🗂️ **Step 2: Manual File Deletion**

**Delete these directories completely**:
```
📁 enhanced_learning_data_v2/
📁 validation_results/
📁 learning_data/
📁 enhanced_learning_data/
📁 learning_metrics/
📁 weight_configurations/
```

**Delete these database files**:
```
🗄️ learning_database.db
🗄️ enhanced_learning.db
🗄️ prediction_data.db
🗄️ weight_performance.db
```

**How to delete**:
1. Navigate to your tennis calculator directory
2. Select the directories/files listed above
3. Press `Delete` key or right-click → Delete
4. If you get "file in use" errors, restart your computer first

## 🔄 **Step 3: Restart Computer (Recommended)**

**Why restart?**
- ✅ Releases all file locks
- ✅ Clears memory caches
- ✅ Ensures clean process state
- ✅ Guarantees complete reset

**After restart**:
1. Navigate back to your tennis calculator directory
2. Verify the directories/files from Step 2 are gone
3. If any remain, delete them now (should work after restart)

## 🚀 **Step 4: Start Fresh**

**Launch the application**:
```bash
python enhanced_gui.py
```

**Verify reset worked**:
1. ✅ **Learning Dashboard** - Click "Learning Dashboard" button
   - Should show all segments with 0 predictions
   - No learned weights should exist

2. ✅ **Statistics Tab** - Check Statistics tab
   - Total predictions should be 0
   - No accuracy history

3. ✅ **AI Menu** - Check AI menu
   - Should show default weights message

## 🧪 **Step 5: Test the System**

**Make a test prediction**:
1. Fill in Input Data tab with tournament level (ATP/Challenger/WTA)
2. Add some match data
3. Click "Analyze Match"
4. Go to "Current Set Prediction" tab
5. Click "Get AI Analysis"

**Verify tournament integration**:
- ✅ AI prompt should include tournament level
- ✅ Prediction should be recorded with tournament info
- ✅ Learning Dashboard should show the new prediction

## 🔧 **Alternative: Safe Reset Script**

If manual deletion is difficult, try the safe reset script:

```bash
python safe_reset_script.py
```

This script:
- ✅ Checks for running processes
- ✅ Handles file locking gracefully
- ✅ Provides better error messages
- ✅ Guides you through the process

## 🆘 **Troubleshooting**

### **Problem: "File in use" errors persist**
**Solution**: 
1. Restart computer
2. Don't open any Python applications
3. Delete files immediately after restart
4. Then start enhanced_gui.py

### **Problem: Directories recreate themselves**
**Solution**:
1. This is normal - the application creates them
2. The important thing is that they're empty
3. Check Learning Dashboard to confirm 0 predictions

### **Problem: Old predictions still appear**
**Solution**:
1. Check if you deleted all database files
2. Look for `.db` files in subdirectories
3. Search for `*.db` files in the entire project folder

### **Problem: AI still uses old weights**
**Solution**:
1. Go to AI Menu → "Reset All Learning Systems"
2. Or restart the application completely
3. Check that default weights are being used

## ✅ **Verification Checklist**

After manual reset, verify these items:

**File System**:
- [ ] `enhanced_learning_data_v2/` directory is empty or recreated
- [ ] `validation_results/` directory is empty or recreated  
- [ ] No `.db` files in project directory
- [ ] No old learning data files remain

**Application State**:
- [ ] Learning Dashboard shows 0 predictions for all segments
- [ ] Statistics tab shows 0 total predictions
- [ ] AI predictions include tournament level in prompts
- [ ] New predictions are recorded with tournament info

**Functionality**:
- [ ] Can make new predictions
- [ ] Tournament level selection works
- [ ] Learning Dashboard updates with new predictions
- [ ] No error messages about missing files

## 🎉 **Success!**

If all verification items pass, you have successfully performed a manual reset! 

**Your system is now**:
- ✅ **Completely clean** - No old learning data
- ✅ **Tournament-ready** - Supports ATP/Challenger/WTA distinction  
- ✅ **Validation-enabled** - Proper cross-validation system
- ✅ **Overfitting-proof** - Statistical significance testing

Start making predictions with tournament levels and watch your enhanced learning system build robust, tournament-specific models! 🎾

## 📞 **Still Having Issues?**

If manual reset doesn't work:

1. **Check file permissions** - Ensure you have write access to the directory
2. **Run as administrator** - Try running Python as administrator
3. **Antivirus interference** - Temporarily disable antivirus during reset
4. **Disk space** - Ensure you have enough free disk space
5. **File corruption** - Run disk check utility

The manual reset should work in 99% of cases after a computer restart! 🚀
