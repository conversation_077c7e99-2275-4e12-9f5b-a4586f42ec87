# Adaptive Learning System for Tennis Predictions

## Overview

The Adaptive Learning System is a comprehensive machine learning framework that continuously improves tennis prediction accuracy by learning from historical results. It automatically adjusts prediction weights based on real-world performance data, creating a self-improving AI system.

## Key Features

### 🧠 **Phase 1: Historical Results Storage**
- **Comprehensive Data Capture**: Records match details, AI predictions, actual outcomes, and context factors
- **Enhanced Prediction Records**: Stores prompt weights, learning metadata, and version control
- **Robust Data Persistence**: SQLite database with JSON backup for reliability
- **Performance Metrics**: Tracks accuracy trends, confidence intervals, and statistical significance

### 📊 **Phase 2: Learning Algorithm Implementation**
- **Weight Correlation Analysis**: Identifies which prompt weights correlate with higher accuracy
- **Pattern Detection**: Analyzes prediction failures by context (surface, set number, score scenarios)
- **Gradient Descent Optimization**: Automatically calculates optimal weight adjustments
- **Statistical Validation**: Prevents overfitting with significance testing and minimum sample sizes

### 🎯 **Phase 3: Dynamic Prompt Adaptation**
- **Context-Aware Weights**: Different weights for different scenarios (clay vs grass, early vs late sets)
- **Automatic Updates**: Real-time weight adjustments after each prediction outcome
- **Manual Override**: User control over automatic adjustments when needed
- **Version Control**: Tracks prompt iterations and their performance over time

## System Architecture

```
Enhanced GUI (enhanced_gui.py)
    ↓
Gemini API (gemini_api.py) ← Adaptive Weights
    ↓
Prediction Weights Manager (prediction_weights_manager.py)
    ↓
Adaptive Learning System (adaptive_learning_system.py)
    ↓
Prediction Tracker (prediction_tracker.py) ← Enhanced with Learning Fields
```

## Core Components

### 1. **AdaptiveLearningSystem** (`adaptive_learning_system.py`)
- Main learning engine that analyzes patterns and optimizes weights
- Implements gradient descent optimization with statistical validation
- Manages learning metrics and performance tracking
- Provides safeguards against overfitting

### 2. **PredictionWeightsManager** (`prediction_weights_manager.py`)
- Manages contextual weight adjustments for different scenarios
- Blends learned weights with contextual modifications
- Handles manual overrides and sensitivity controls
- Tracks performance by context (surface, set number, etc.)

### 3. **Enhanced PredictionTracker** (`prediction_tracker.py`)
- Extended with adaptive learning fields:
  - `prompt_weights`: Weights used for each prediction
  - `context_factors`: Match context and environmental factors
  - `learning_metadata`: Learning system metadata
  - `prompt_version`: Version tracking for prompt iterations
  - `weight_source`: Source of weights (learned, manual, default)

### 4. **Learning Metrics Dashboard** (`learning_metrics_dashboard.py`)
- Real-time monitoring of learning system performance
- Manual weight configuration interface
- Performance analysis by context (surface, set, score type)
- Export/import functionality for learning data

## Integration with Enhanced GUI

The adaptive learning system is seamlessly integrated into the main tennis calculator:

### Menu Integration
- **Tools → AI Learning System → Learning Dashboard**: Opens comprehensive monitoring interface
- **Tools → AI Learning System → Optimize Weights Now**: Manually triggers weight optimization

### Automatic Learning
- Every AI prediction automatically uses adaptive weights
- Prediction outcomes are recorded for continuous learning
- Weight optimization occurs automatically every 10 predictions
- No user intervention required for basic operation

## Weight Configuration

### Default Weights (Research-Based)
```python
service_consistency_weight = 0.25    # Service consistency impact
mental_fatigue_weight = 0.15         # Mental fatigue differential
service_pressure_weight = 0.15       # Pressure serving situations
momentum_intensity_weight = 0.20     # Current momentum state
clutch_performance_weight = 0.05     # Clutch performance baseline
current_hold_streak_weight = 0.10    # Current service hold streak
deuce_game_performance_weight = 0.10 # Deuce game performance
```

### Contextual Adjustments

#### Surface-Specific Modifications
- **Clay Courts**: Increased mental fatigue weight (+0.05), reduced momentum weight (-0.03)
- **Grass Courts**: Increased service consistency (+0.08) and momentum (+0.05)
- **Hard Courts**: Balanced weights with minimal adjustments

#### Set Number Adjustments
- **Set 1**: Reduced fatigue impact (-0.05), increased momentum (+0.03)
- **Set 3+**: Increased fatigue impact (+0.08 to +0.15), enhanced clutch performance (+0.03 to +0.08)

#### Score Context Modifications
- **Critical Tied Scores (5-5, 6-6)**: Maximum clutch performance and pressure weights
- **Break Point Scenarios**: Enhanced service pressure and clutch performance
- **Early Tied Scores (3-3, 4-4)**: Increased momentum and consistency weights

## Learning Process

### 1. **Data Collection**
- Each AI prediction stores the weights used and context factors
- Actual match outcomes are recorded when available
- Minimum 20 predictions required before optimization begins

### 2. **Pattern Analysis**
- Analyzes accuracy by surface, set number, score type, and confidence level
- Identifies underperforming contexts and weight combinations
- Calculates statistical significance of patterns

### 3. **Weight Optimization**
- Uses logistic regression to find optimal weight combinations
- Applies learning rate and maximum change constraints
- Validates improvements with statistical significance testing

### 4. **Continuous Adaptation**
- Automatically updates weights when significant improvements are found
- Maintains version control of weight configurations
- Tracks accuracy trends over time

## Performance Monitoring

### Key Metrics
- **Overall Accuracy**: Current prediction accuracy percentage
- **Improvement Rate**: Rate of accuracy improvement per prediction
- **Sample Size**: Number of predictions used for analysis
- **Context Performance**: Accuracy breakdown by surface, set, and score type

### Dashboard Features
- Real-time performance visualization
- Weight configuration management
- Historical trend analysis
- Export/import capabilities for learning data

## Configuration Options

### Learning Parameters
- **Minimum Sample Size**: Default 20 predictions (configurable)
- **Significance Threshold**: Default 0.05 for statistical tests
- **Maximum Weight Change**: Default 0.1 per optimization iteration
- **Learning Rate**: Default 0.01 for gradient descent

### User Controls
- **Auto-Adjustment**: Enable/disable automatic weight updates
- **Manual Override**: Set custom weights that override learning
- **Adjustment Sensitivity**: Control how aggressively weights are modified (0.0-1.0)

## Data Storage

### File Structure
```
learning_data/
├── weight_configurations.json    # Current and historical weight configs
├── learning_metrics.json         # Performance metrics and trends
└── learning_database.db         # SQLite database for detailed analysis

weights_data/
├── contextual_weights.json      # Context-specific weight adjustments
└── performance_history.json     # Historical performance data
```

### Database Schema
- **weight_performance**: Tracks weight configurations and their performance
- **learning_sessions**: Records optimization sessions and improvements

## Usage Examples

### Basic Usage (Automatic)
The system works automatically once integrated. Simply use the AI analysis feature in enhanced_gui.py, and the system will:
1. Apply adaptive weights to predictions
2. Record outcomes when provided
3. Optimize weights automatically every 10 predictions

### Manual Weight Configuration
```python
from learning_metrics_dashboard import LearningMetricsDashboard

# Open dashboard
dashboard = LearningMetricsDashboard()
dashboard.show()

# Use "Manual Configuration" button to set custom weights
```

### Programmatic Access
```python
from adaptive_learning_system import AdaptiveLearningSystem

# Initialize system
learning_system = AdaptiveLearningSystem()

# Get current performance
status = learning_system.get_learning_status()
print(f"Current accuracy: {status['learning_metrics']['accuracy']:.1f}%")

# Manually trigger optimization
result = learning_system.optimize_weights()
print(f"Optimization result: {result['status']}")
```

## Safeguards and Limitations

### Overfitting Prevention
- Minimum sample size requirements before optimization
- Statistical significance testing for weight changes
- Maximum weight change limits per iteration
- Cross-validation of improvements

### Data Quality
- Robust error handling for corrupted data
- Automatic backup and recovery systems
- Validation of prediction records before processing

### User Control
- Manual override capability for automatic adjustments
- Sensitivity controls for adjustment aggressiveness
- Reset functionality to return to default weights

## Future Enhancements

### Planned Features
- **Multi-Model Ensemble**: Integration with multiple AI models for comparison
- **Advanced Context Factors**: Weather, player fatigue, head-to-head history
- **Real-Time Learning**: Immediate weight updates during live matches
- **Predictive Modeling**: Forecast optimal weights for upcoming matches

### Research Opportunities
- **Deep Learning Integration**: Neural networks for weight optimization
- **Bayesian Optimization**: More sophisticated optimization algorithms
- **Transfer Learning**: Apply learnings across different tournaments/surfaces

## Troubleshooting

### Common Issues
1. **Insufficient Data**: Need at least 20 AI predictions for optimization
2. **No Significant Improvement**: Current weights may already be optimal
3. **Import Errors**: Ensure all dependencies are installed (scipy, sklearn, pandas)

### Performance Tips
- Allow system to collect data for several days before expecting improvements
- Use manual configuration for immediate weight adjustments if needed
- Monitor dashboard regularly to track learning progress

## Technical Requirements

### Dependencies
- Python 3.8+
- PyQt5 (for GUI components)
- NumPy (numerical computations)
- Pandas (data analysis)
- Scikit-learn (machine learning algorithms)
- SciPy (statistical functions)

### Integration
The system is designed to integrate seamlessly with the existing tennis calculator codebase. All components are backward-compatible and can be disabled if needed.

---

**Note**: The adaptive learning system represents a significant advancement in tennis prediction accuracy. By continuously learning from real-world results, it creates a self-improving AI that becomes more accurate over time, providing users with increasingly reliable predictions for their tennis betting and analysis needs.
