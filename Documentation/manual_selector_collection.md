# Manual Selector Collection Guide

## 🎯 Target Elements to Find

Based on your tennis live scores page, you need to find selectors for these elements:

### 1. Match Container (Each Row)
- **What to select**: Each complete match row
- **Look for**: The `<tr>` or container that wraps each match
- **Example**: `tr`, `.match-row`, `tbody tr`

### 2. Tournament Name
- **What to select**: Tournament names like "WTA Iasi", "Challenger San Marino"
- **Look for**: Text elements in the left column
- **Example**: `.tournament`, `td:first-child a`, `.tournament-name`

### 3. Player 1 Name
- **What to select**: First player name (left side of vs)
- **Look for**: Player names in the middle-left area
- **Example**: `.player1`, `.home-player`, `td:nth-child(2)`

### 4. Player 2 Name  
- **What to select**: Second player name (right side of vs)
- **Look for**: Player names in the middle-right area
- **Example**: `.player2`, `.away-player`, `td:nth-child(3)`

### 5. Current Score
- **What to select**: Scores like "0-0", "1-2", "4-2"
- **Look for**: Score displays, usually in blue text
- **Example**: `.score`, `.current-score`, `td:nth-child(4)`

## 🔍 How to Use SelectorsHub

### Step 1: Open the Tennis Page
1. Go to https://betsapi.com/cio/tennis
2. Make sure you can see live tennis matches
3. Open Chrome DevTools (F12)

### Step 2: For Each Element Type
1. **Right-click** on the element you want to select
2. **Click SelectorsHub icon** in the context menu
3. **Choose CSS Selector** (preferred) or **Rel XPath** (backup)
4. **Copy the selector** that SelectorsHub generates
5. **Test it** by using Ctrl+F in DevTools and pasting the selector

### Step 3: Verify Selectors
- Make sure each selector finds **multiple matches** (not just one)
- Test on different matches to ensure consistency
- Prefer shorter, simpler selectors when possible

## 📝 Selector Collection Template

Copy this template and fill in your selectors:

```json
{
  "match_container": "YOUR_MATCH_ROW_SELECTOR_HERE",
  "tournament": "YOUR_TOURNAMENT_SELECTOR_HERE", 
  "player1": "YOUR_PLAYER1_SELECTOR_HERE",
  "player2": "YOUR_PLAYER2_SELECTOR_HERE",
  "score": "YOUR_SCORE_SELECTOR_HERE"
}
```

## ⚠️ Important Tips

### CSS Selector Preferences (in order):
1. **Class names**: `.match-row`, `.tournament`
2. **Element + class**: `td.player-name`
3. **Nth-child**: `td:nth-child(2)`
4. **Attribute selectors**: `[data-match-id]`

### XPath Preferences (if you must use XPath):
1. **Rel XPath**: `//td[@class='tournament']`
2. **Avoid Index XPath**: Too brittle
3. **Avoid AbsXpath**: Changes with page structure

### Testing Your Selectors:
- Open DevTools Console
- Test with: `document.querySelectorAll('YOUR_SELECTOR')`
- Should return multiple elements (one per match)

## 🚀 Next Steps

After collecting selectors:
1. Save them to `betsapi_selectors.json`
2. Run `python monitor_gui.py` to test
3. Check if matches are detected correctly
