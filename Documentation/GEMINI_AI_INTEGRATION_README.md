# Gemini AI Integration for Tennis Set Predictions

## Overview

The tennis calculator now includes AI-powered set predictions using Google's Gemini 2.5 Flash model. This feature provides intelligent analysis of tennis matches by considering serving patterns, momentum factors, and match context to predict set winners.

## Features

- **AI-Powered Analysis**: Uses Gemini 2.5 Flash to analyze tennis match situations
- **Comprehensive Data Integration**: Incorporates serving patterns, momentum indicators, and match context
- **Natural Language Analysis**: Provides detailed explanations of predictions
- **Probability Extraction**: Automatically parses AI predictions into probability percentages
- **Secure API Key Management**: Supports environment variables and in-app configuration

## Setup Instructions

### 1. Get a Gemini API Key

1. Visit [Google AI Studio](https://ai.google.dev/gemini-api/docs/api-key)
2. Sign in with your Google account
3. Create a new API key
4. Copy the API key for configuration

### 2. Configure the API Key

**Option A: Environment Variable (Recommended)**
```bash
# Windows
set GEMINI_API_KEY=your_api_key_here

# Linux/Mac
export GEMINI_API_KEY=your_api_key_here
```

**Option B: In-App Configuration**
1. Open the tennis calculator application
2. Go to the "Current Set Prediction" tab
3. Enter your API key in the "Gemini API Key" field
4. Click "Set API Key"

## How to Use

### 1. Analyze a Tennis Match

1. Enter player information (names and codes)
2. Paste point-by-point match data
3. Set match context (format, set number, surface)
4. Click "Analyze Match"

### 2. Get AI Set Prediction

1. Ensure at least 6 games have been played
2. Go to the "Current Set Prediction" tab
3. Verify the API key is configured (green status)
4. Click "Get AI Analysis"

### 3. Review Results

The AI analysis provides:
- **Detailed Analysis**: Natural language explanation of the match situation
- **Key Factors**: Important momentum and serving pattern insights
- **Probability Predictions**: Percentage chances for each player
- **Reasoning**: Explanation of the prediction logic

## Data Used in AI Analysis

The AI considers the following factors:

### Serving Patterns
- Current momentum state (strong_serving, weak_serving, etc.)
- Consecutive 0-15 starts
- Recent 3-point runs
- Break points faced in the set
- Games held percentage
- Momentum intensity score (0-10)
- Service pressure index (0-10)
- Clutch performance rate
- Mental fatigue level

### Match Context
- Current set number
- Current score (games won by each player)
- Match format (Best of 3 or Best of 5)
- Court surface type
- Previous sets winners (if applicable)

### Example AI Prompt

```
You are an expert tennis analyst. Analyze this tennis match situation:

This tennis match is at Set 1, and the current score is 5-5.
Match Context: Bo3 format on Hard surface

Here are the detailed serving patterns and momentum factors for both players:

Rafael Nadal (NAV) Serving Analysis:
- Current Momentum: strong_serving
- Games Held: 85%
- Consecutive 0-15 starts: 0
- Recent 3-point runs: 2
- Break points faced: 1
- Momentum Intensity: 7.5/10
- Service Pressure Index: 6.2/10
- Clutch Performance: 75%
- Mental Fatigue Level: 25%

[Similar analysis for opponent]

Based on this comprehensive analysis, which player has more chances of winning this set?
Please provide detailed reasoning and end with:
Rafael Nadal: XX%
Pablo Carreno Busta: XX%
```

## Technical Implementation

### Files Added/Modified

1. **`config.py`** - Configuration management for API keys
2. **`gemini_api.py`** - Gemini API integration and analysis logic
3. **`enhanced_gui.py`** - GUI integration for AI analysis
4. **`test_gemini_integration.py`** - Test suite for AI functionality

### Key Components

- **GeminiTennisAnalyzer**: Main class for AI analysis
- **Config**: Secure configuration management
- **API Integration**: Uses `google-generativeai` library
- **Error Handling**: Graceful fallbacks and user feedback

## Troubleshooting

### Common Issues

**"API key required" error**
- Ensure your API key is properly set
- Check environment variable or in-app configuration
- Verify the API key is valid

**"Insufficient Data" error**
- Ensure at least 6 games have been played
- Verify match data has been analyzed first

**API request failures**
- Check internet connection
- Verify API key has sufficient quota
- Check Google AI Studio for service status

### Testing

Run the test suite to verify functionality:
```bash
python test_gemini_integration.py
```

## Cost Considerations

- Gemini 2.5 Pro has usage-based pricing
- Each analysis uses approximately 1,500-2,000 tokens
- Monitor usage through Google AI Studio
- Consider setting usage limits if needed

## Privacy and Security

- API keys are not saved to configuration files
- Use environment variables for production deployments
- Match data is sent to Google's servers for analysis
- Review Google's privacy policy for data handling

## Future Enhancements

Potential improvements for future versions:
- Caching of similar match situations
- Batch analysis for multiple matches
- Integration with other AI models
- Historical performance tracking
- Custom prompt templates

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review Google AI documentation
3. Test with the provided test script
4. Verify API key configuration
