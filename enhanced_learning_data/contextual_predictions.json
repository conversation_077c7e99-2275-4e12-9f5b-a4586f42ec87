[{"prediction_id": "pred_20250729_165855_276449", "timestamp": "2025-07-29T16:58:55.276449", "set_number": 1, "score": [4, 4], "surface": "<PERSON>", "predicted_winner": "<PERSON>", "actual_winner": "DAR", "historical_weight_used": 0.51, "momentum_weight_used": 0.49, "historical_factors": {"Camilo Ugo Carabelli_break_point_conversion": 45.0, "Camilo Ugo Carabelli_break_point_save": 60.2, "Camilo Ugo Carabelli_service_hold_rate": 70.3, "Camilo Ugo Carabelli_surface_win_rate": 50.0, "Camilo Ugo Carabelli_clutch_performance": 75.0, "break_point_conversion_advantage": -5.0, "service_hold_advantage": -4.700000000000003, "clutch_performance_advantage": 25.0}, "momentum_factors": {"momentum_intensity_advantage": 1.6999999999999993, "service_consistency_advantage": 0.0, "mental_fatigue_advantage": 0.0, "recent_momentum_advantage": 0, "current_clutch_advantage": 20.0}, "was_correct": true, "confidence_level": 0.515, "session_id": null, "match_status": "completed", "is_ai_prediction": true, "data_quality_assessment": null, "metrics_quality": null, "quality_adjusted_weights": null, "prediction_reliability_tier": "medium"}, {"prediction_id": "pred_20250729_172615_089240", "timestamp": "2025-07-29T17:26:15.089240", "set_number": 1, "score": [3, 3], "surface": "<PERSON>", "predicted_winner": "<PERSON>", "actual_winner": null, "historical_weight_used": 0.51, "momentum_weight_used": 0.49, "historical_factors": {"Camilo Ugo Carabelli_break_point_conversion": 45.0, "Camilo Ugo Carabelli_break_point_save": 60.2, "Camilo Ugo Carabelli_service_hold_rate": 70.3, "Camilo Ugo Carabelli_surface_win_rate": 50.0, "Camilo Ugo Carabelli_clutch_performance": 75.0, "break_point_conversion_advantage": -5.0, "service_hold_advantage": -4.700000000000003, "clutch_performance_advantage": 25.0}, "momentum_factors": {"momentum_intensity_advantage": -0.2079999999999993, "service_consistency_advantage": 0.0, "mental_fatigue_advantage": 0.0, "recent_momentum_advantage": 1, "current_clutch_advantage": 20.0}, "was_correct": null, "confidence_level": 0.515, "session_id": null, "match_status": "pending", "is_ai_prediction": true, "data_quality_assessment": {"overall_confidence": 0.5772652515247156, "overall_reliability": "medium", "quality_warnings": [], "recommendation": "Moderate quality - consider additional context", "metric_count": 5, "reliability_distribution": {"high": 0, "medium": 3, "low": 2}}, "metrics_quality": {"Camilo Ugo Carabelli_break_point_conversion": {"confidence_score": 0.4924747022113695, "data_source": "historical", "sample_size": 10, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:26:13.750180", "blend_ratio": 0.0}, "Camilo Ugo Carabelli_break_point_save": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:26:13.750180", "blend_ratio": 0.0}, "Camilo Ugo Carabelli_service_hold_rate": {"confidence_score": 0.6530127703135442, "data_source": "historical", "sample_size": 15, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:26:13.750180", "blend_ratio": 0.0}, "Camilo Ugo Carabelli_surface_win_rate": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:26:13.750180", "blend_ratio": 0.0}, "Camilo Ugo Carabelli_clutch_performance": {"confidence_score": 0.476, "data_source": "historical", "sample_size": 8, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:26:13.750180", "blend_ratio": 0.0}}, "quality_adjusted_weights": {"break_point_conversion": 0.25, "service_consistency": 0.2, "mental_fatigue": 0.15, "clutch_performance": 0.15, "momentum_intensity": 0.1, "service_pressure": 0.15}, "prediction_reliability_tier": "medium"}, {"prediction_id": "pred_20250729_172751_459630", "timestamp": "2025-07-29T17:27:51.459630", "set_number": 1, "score": [4, 4], "surface": "<PERSON>", "predicted_winner": "<PERSON>", "actual_winner": "DAR", "historical_weight_used": 0.51, "momentum_weight_used": 0.49, "historical_factors": {"Camilo Ugo Carabelli_break_point_conversion": 45.0, "Camilo Ugo Carabelli_break_point_save": 60.2, "Camilo Ugo Carabelli_service_hold_rate": 70.3, "Camilo Ugo Carabelli_surface_win_rate": 50.0, "Camilo Ugo Carabelli_clutch_performance": 75.0, "break_point_conversion_advantage": -5.0, "service_hold_advantage": -4.700000000000003, "clutch_performance_advantage": 25.0}, "momentum_factors": {"momentum_intensity_advantage": 1.6999999999999993, "service_consistency_advantage": 0.0, "mental_fatigue_advantage": 0.0, "recent_momentum_advantage": 0, "current_clutch_advantage": 20.0}, "was_correct": true, "confidence_level": 0.515, "session_id": null, "match_status": "completed", "is_ai_prediction": true, "data_quality_assessment": {"overall_confidence": 0.5772652515247156, "overall_reliability": "medium", "quality_warnings": [], "recommendation": "Moderate quality - consider additional context", "metric_count": 5, "reliability_distribution": {"high": 0, "medium": 3, "low": 2}}, "metrics_quality": {"Camilo Ugo Carabelli_break_point_conversion": {"confidence_score": 0.4924747022113695, "data_source": "historical", "sample_size": 10, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:27:50.927593", "blend_ratio": 0.0}, "Camilo Ugo Carabelli_break_point_save": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:27:50.927593", "blend_ratio": 0.0}, "Camilo Ugo Carabelli_service_hold_rate": {"confidence_score": 0.6530127703135442, "data_source": "historical", "sample_size": 15, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:27:50.927593", "blend_ratio": 0.0}, "Camilo Ugo Carabelli_surface_win_rate": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:27:50.927593", "blend_ratio": 0.0}, "Camilo Ugo Carabelli_clutch_performance": {"confidence_score": 0.476, "data_source": "historical", "sample_size": 8, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:27:50.927593", "blend_ratio": 0.0}}, "quality_adjusted_weights": {"break_point_conversion": 0.25, "service_consistency": 0.2, "mental_fatigue": 0.15, "clutch_performance": 0.15, "momentum_intensity": 0.1, "service_pressure": 0.15}, "prediction_reliability_tier": "medium"}, {"prediction_id": "pred_20250729_173408_073018", "timestamp": "2025-07-29T17:34:08.073018", "set_number": 1, "score": [3, 3], "surface": "Hard", "predicted_winner": "<PERSON>", "actual_winner": null, "historical_weight_used": 0.51, "momentum_weight_used": 0.49, "historical_factors": {"Arthur Rinderknech_break_point_conversion": 45.0, "Arthur Rinderknech_break_point_save": 59.6, "Arthur Rinderknech_service_hold_rate": 74.9, "Arthur Rinderknech_surface_win_rate": 50.0, "Arthur Rinderknech_clutch_performance": 75.0, "break_point_conversion_advantage": -5.0, "service_hold_advantage": -0.09999999999999432, "clutch_performance_advantage": 25.0}, "momentum_factors": {"momentum_intensity_advantage": 5.470000000000001, "service_consistency_advantage": 0.0, "mental_fatigue_advantage": 0.0, "recent_momentum_advantage": 1, "current_clutch_advantage": 0.0}, "was_correct": null, "confidence_level": 0.532, "session_id": null, "match_status": "pending", "is_ai_prediction": true, "data_quality_assessment": {"overall_confidence": 0.5772652515247156, "overall_reliability": "medium", "quality_warnings": [], "recommendation": "Moderate quality - consider additional context", "metric_count": 5, "reliability_distribution": {"high": 0, "medium": 3, "low": 2}}, "metrics_quality": {"Arthur Rinderknech_break_point_conversion": {"confidence_score": 0.4924747022113695, "data_source": "historical", "sample_size": 10, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:07.206253", "blend_ratio": 0.0}, "Arthur Rinderknech_break_point_save": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:07.206253", "blend_ratio": 0.0}, "Arthur Rinderknech_service_hold_rate": {"confidence_score": 0.6530127703135442, "data_source": "historical", "sample_size": 15, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:07.206253", "blend_ratio": 0.0}, "Arthur Rinderknech_surface_win_rate": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:07.206253", "blend_ratio": 0.0}, "Arthur Rinderknech_clutch_performance": {"confidence_score": 0.476, "data_source": "historical", "sample_size": 8, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:07.206253", "blend_ratio": 0.0}}, "quality_adjusted_weights": {"break_point_conversion": 0.25, "service_consistency": 0.2, "mental_fatigue": 0.15, "clutch_performance": 0.15, "momentum_intensity": 0.1, "service_pressure": 0.15}, "prediction_reliability_tier": "medium"}, {"prediction_id": "pred_20250729_173424_183588", "timestamp": "2025-07-29T17:34:24.183588", "set_number": 1, "score": [4, 4], "surface": "Hard", "predicted_winner": "<PERSON>", "actual_winner": null, "historical_weight_used": 0.51, "momentum_weight_used": 0.49, "historical_factors": {"Arthur Rinderknech_break_point_conversion": 45.0, "Arthur Rinderknech_break_point_save": 59.6, "Arthur Rinderknech_service_hold_rate": 74.9, "Arthur Rinderknech_surface_win_rate": 50.0, "Arthur Rinderknech_clutch_performance": 75.0, "break_point_conversion_advantage": -5.0, "service_hold_advantage": -0.09999999999999432, "clutch_performance_advantage": 25.0}, "momentum_factors": {"momentum_intensity_advantage": 0.6999999999999993, "service_consistency_advantage": 0.0, "mental_fatigue_advantage": 0.0, "recent_momentum_advantage": 0, "current_clutch_advantage": 50.0}, "was_correct": null, "confidence_level": 0.532, "session_id": null, "match_status": "pending", "is_ai_prediction": true, "data_quality_assessment": {"overall_confidence": 0.5772652515247156, "overall_reliability": "medium", "quality_warnings": [], "recommendation": "Moderate quality - consider additional context", "metric_count": 5, "reliability_distribution": {"high": 0, "medium": 3, "low": 2}}, "metrics_quality": {"Arthur Rinderknech_break_point_conversion": {"confidence_score": 0.4924747022113695, "data_source": "historical", "sample_size": 10, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:23.674877", "blend_ratio": 0.0}, "Arthur Rinderknech_break_point_save": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:23.674877", "blend_ratio": 0.0}, "Arthur Rinderknech_service_hold_rate": {"confidence_score": 0.6530127703135442, "data_source": "historical", "sample_size": 15, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:23.674877", "blend_ratio": 0.0}, "Arthur Rinderknech_surface_win_rate": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:23.674877", "blend_ratio": 0.0}, "Arthur Rinderknech_clutch_performance": {"confidence_score": 0.476, "data_source": "historical", "sample_size": 8, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:23.674877", "blend_ratio": 0.0}}, "quality_adjusted_weights": {"break_point_conversion": 0.25, "service_consistency": 0.2, "mental_fatigue": 0.15, "clutch_performance": 0.15, "momentum_intensity": 0.1, "service_pressure": 0.15}, "prediction_reliability_tier": "medium"}, {"prediction_id": "pred_20250729_173435_384447", "timestamp": "2025-07-29T17:34:35.384447", "set_number": 1, "score": [5, 5], "surface": "Hard", "predicted_winner": "<PERSON>", "actual_winner": null, "historical_weight_used": 0.51, "momentum_weight_used": 0.49, "historical_factors": {"Arthur Rinderknech_break_point_conversion": 45.0, "Arthur Rinderknech_break_point_save": 59.6, "Arthur Rinderknech_service_hold_rate": 74.9, "Arthur Rinderknech_surface_win_rate": 50.0, "Arthur Rinderknech_clutch_performance": 75.0, "break_point_conversion_advantage": -5.0, "service_hold_advantage": -0.09999999999999432, "clutch_performance_advantage": 25.0}, "momentum_factors": {"momentum_intensity_advantage": 0.0, "service_consistency_advantage": 0.0, "mental_fatigue_advantage": 0.0, "recent_momentum_advantage": 0, "current_clutch_advantage": 16.666666666666657}, "was_correct": null, "confidence_level": 0.531, "session_id": null, "match_status": "pending", "is_ai_prediction": true, "data_quality_assessment": {"overall_confidence": 0.5772652515247156, "overall_reliability": "medium", "quality_warnings": [], "recommendation": "Moderate quality - consider additional context", "metric_count": 5, "reliability_distribution": {"high": 0, "medium": 3, "low": 2}}, "metrics_quality": {"Arthur Rinderknech_break_point_conversion": {"confidence_score": 0.4924747022113695, "data_source": "historical", "sample_size": 10, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:34.621158", "blend_ratio": 0.0}, "Arthur Rinderknech_break_point_save": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:34.621158", "blend_ratio": 0.0}, "Arthur Rinderknech_service_hold_rate": {"confidence_score": 0.6530127703135442, "data_source": "historical", "sample_size": 15, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:34.621158", "blend_ratio": 0.0}, "Arthur Rinderknech_surface_win_rate": {"confidence_score": 0.6324193925493322, "data_source": "historical", "sample_size": 12, "reliability_tier": "medium", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:34.621158", "blend_ratio": 0.0}, "Arthur Rinderknech_clutch_performance": {"confidence_score": 0.476, "data_source": "historical", "sample_size": 8, "reliability_tier": "low", "calculation_method": "historical_data", "last_updated": "2025-07-29T17:34:34.622155", "blend_ratio": 0.0}}, "quality_adjusted_weights": {"break_point_conversion": 0.25, "service_consistency": 0.2, "mental_fatigue": 0.15, "clutch_performance": 0.15, "momentum_intensity": 0.1, "service_pressure": 0.15}, "prediction_reliability_tier": "medium"}]