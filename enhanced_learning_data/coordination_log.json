[{"operation_type": "unified_validation", "priority": 1, "requested_at": "2025-07-29T19:37:10.731203", "system_name": "UnifiedValidationSystem", "estimated_duration_minutes": 15, "metadata": {"prediction_count": 750, "completed_at": "2025-07-29T19:37:10.771205", "success": true, "results": {"status": "success", "current_effective_ratios": {"Clay_1_mid": {"intended_historical": 0.5485234899328859, "intended_momentum": 0.4514765100671141, "effective_historical": 0.5030962610954485, "effective_momentum": 0.4969037389045514, "deviation": 0.04542722883743744, "sample_size": 298}, "Clay_1_late": {"intended_historical": 0.4789583333333333, "intended_momentum": 0.5210416666666667, "effective_historical": 0.4337572167088034, "effective_momentum": 0.5662427832911966, "deviation": 0.04520111662452991, "sample_size": 48}, "Clay_2_mid": {"intended_historical": 0.5707364341085271, "intended_momentum": 0.42926356589147285, "effective_historical": 0.5256112796716045, "effective_momentum": 0.47438872032839546, "deviation": 0.045125154436922665, "sample_size": 258}, "Clay_2_late": {"intended_historical": 0.49279069767441863, "intended_momentum": 0.5072093023255814, "effective_historical": 0.44740509269034245, "effective_momentum": 0.5525949073096574, "deviation": 0.04538560498407618, "sample_size": 43}, "Clay_3_mid": {"intended_historical": 0.4684615384615385, "intended_momentum": 0.5315384615384615, "effective_historical": 0.42344597413433466, "effective_momentum": 0.5765540258656655, "deviation": 0.04501556432720383, "sample_size": 91}, "Clay_3_late": {"intended_historical": 0.4133333333333333, "intended_momentum": 0.5866666666666668, "effective_historical": 0.3699284009546539, "effective_momentum": 0.6300715990453462, "deviation": 0.04340493237867937, "sample_size": 12}}, "optimization_results": {"best_combination": {"intended_historical": 0.6, "intended_momentum": 0.4, "weight_boost": 0.15, "effective_historical": 0.5660377358490566, "effective_momentum": 0.43396226415094336, "simulated_accuracy": 0.6453333333333333, "constrained": false}, "tested_combinations": 25}, "validation_results": {"has_improvements": true, "current_accuracy": 0.5546666666666666, "projected_accuracy": 0.6453333333333333, "improvement": 0.09066666666666667, "is_statistically_significant": true, "sample_size": 750}, "recommendations": ["✅ Apply the optimized balance ratios and weight constraints", "📈 Expected accuracy improvement: 9.1%"]}}}]