"""
Enhanced Player Data Quality Validation System

This module provides comprehensive validation for downloaded player profiles,
including file size checks, content validation, and data quality scoring.
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass


@dataclass
class ValidationResult:
    """Result of player data validation"""
    is_valid: bool
    file_size_bytes: int
    quality_score: float  # 0-100 scale
    issues: List[str]
    warnings: List[str]
    essential_data_present: bool
    match_count: int


class PlayerDataQualityValidator:
    """Validates quality of downloaded player profile data"""
    
    # Minimum file size threshold (20KB as mentioned by user)
    MIN_FILE_SIZE_BYTES = 20 * 1024  # 20KB
    
    # Essential data patterns that should be present in a valid profile
    ESSENTIAL_PATTERNS = {
        'player_name': r'TENNIS PLAYER PROFILE:\s*(.+)',
        'biographical_section': r'BIOGRAPHICAL INFORMATION',
        'age': r'Age:\s*(\d+)',
        'height': r'Height:\s*(\d+)\s*cm',
        'country': r'country:\s*([A-Z]{3})',
        'current_rank': r'Current rank:\s*(\d+)',
        'matches_section': r'MATCH HISTORY|MATCHES PLAYED',
    }
    
    # Optional but valuable data patterns
    VALUABLE_PATTERNS = {
        'peak_rank': r'Peak rank:\s*(\d+)',
        'elo_rating': r'Elo rank:\s*\d+\s*\(rating:\s*(\d+)\)',
        'playing_hand': r'Plays:\s*(Left|Right)',
        'surface_breakdown': r'Surface Breakdown:',
        'recent_matches': r'MATCH \d+:',
        'tournament_data': r'Tournament:',
        'opponent_data': r'Opponent:',
    }
    
    def __init__(self):
        self.validation_cache = {}
    
    def validate_player_file(self, file_path: str) -> ValidationResult:
        """
        Comprehensive validation of a player profile file
        
        Args:
            file_path: Path to the player profile file
            
        Returns:
            ValidationResult with detailed validation information
        """
        file_path = Path(file_path)
        
        # Check if file exists
        if not file_path.exists():
            return ValidationResult(
                is_valid=False,
                file_size_bytes=0,
                quality_score=0.0,
                issues=["File does not exist"],
                warnings=[],
                essential_data_present=False,
                match_count=0
            )
        
        # Get file size
        file_size = file_path.stat().st_size
        
        # Read file content
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                file_size_bytes=file_size,
                quality_score=0.0,
                issues=[f"Cannot read file: {str(e)}"],
                warnings=[],
                essential_data_present=False,
                match_count=0
            )
        
        # Perform validation checks
        issues = []
        warnings = []
        quality_score = 0.0
        
        # File size check
        if file_size < self.MIN_FILE_SIZE_BYTES:
            issues.append(f"File size ({file_size} bytes) is below minimum threshold ({self.MIN_FILE_SIZE_BYTES} bytes)")
        else:
            quality_score += 20  # 20 points for adequate file size
        
        # Essential data validation
        essential_score, essential_issues, essential_present = self._validate_essential_data(content)
        quality_score += essential_score
        issues.extend(essential_issues)
        
        # Valuable data validation
        valuable_score, valuable_warnings = self._validate_valuable_data(content)
        quality_score += valuable_score
        warnings.extend(valuable_warnings)
        
        # Match data validation
        match_score, match_count, match_issues = self._validate_match_data(content)
        quality_score += match_score
        if match_issues:
            warnings.extend(match_issues)
        
        # Content structure validation
        structure_score, structure_issues = self._validate_content_structure(content)
        quality_score += structure_score
        if structure_issues:
            warnings.extend(structure_issues)
        
        # Determine if file is valid
        is_valid = (
            file_size >= self.MIN_FILE_SIZE_BYTES and
            essential_present and
            len(issues) == 0 and
            quality_score >= 60.0  # Minimum quality threshold
        )
        
        return ValidationResult(
            is_valid=is_valid,
            file_size_bytes=file_size,
            quality_score=min(100.0, quality_score),
            issues=issues,
            warnings=warnings,
            essential_data_present=essential_present,
            match_count=match_count
        )
    
    def _validate_essential_data(self, content: str) -> Tuple[float, List[str], bool]:
        """Validate presence of essential data patterns"""
        score = 0.0
        issues = []
        found_patterns = 0
        
        for pattern_name, pattern in self.ESSENTIAL_PATTERNS.items():
            if re.search(pattern, content, re.IGNORECASE):
                found_patterns += 1
                score += 5.0  # 5 points per essential pattern
            else:
                issues.append(f"Missing essential data: {pattern_name}")
        
        essential_present = found_patterns >= len(self.ESSENTIAL_PATTERNS) * 0.7  # 70% of essential data
        
        return score, issues, essential_present
    
    def _validate_valuable_data(self, content: str) -> Tuple[float, List[str]]:
        """Validate presence of valuable but optional data"""
        score = 0.0
        warnings = []
        
        for pattern_name, pattern in self.VALUABLE_PATTERNS.items():
            if re.search(pattern, content, re.IGNORECASE):
                score += 2.0  # 2 points per valuable pattern
            else:
                warnings.append(f"Missing valuable data: {pattern_name}")
        
        return score, warnings
    
    def _validate_match_data(self, content: str) -> Tuple[float, int, List[str]]:
        """Validate match history data quality"""
        score = 0.0
        issues = []
        
        # Count matches
        match_patterns = re.findall(r'MATCH \d+:', content)
        match_count = len(match_patterns)
        
        if match_count == 0:
            issues.append("No match history found")
        elif match_count < 5:
            issues.append(f"Limited match history: only {match_count} matches")
            score += 5.0
        elif match_count < 20:
            score += 10.0
        else:
            score += 15.0  # Excellent match history
        
        # Check for match details
        if re.search(r'Score:', content):
            score += 5.0
        if re.search(r'Tournament:', content):
            score += 5.0
        if re.search(r'Surface:', content):
            score += 5.0
        
        return score, match_count, issues
    
    def _validate_content_structure(self, content: str) -> Tuple[float, List[str]]:
        """Validate overall content structure and organization"""
        score = 0.0
        issues = []
        
        # Check for proper sections
        if re.search(r'BIOGRAPHICAL INFORMATION', content):
            score += 5.0
        else:
            issues.append("Missing biographical information section")
        
        if re.search(r'SUMMARY STATISTICS', content):
            score += 5.0
        else:
            issues.append("Missing summary statistics section")
        
        # Check for proper formatting
        if re.search(r'=+', content):  # Header separators
            score += 2.0
        
        if re.search(r'-+', content):  # Section separators
            score += 2.0
        
        # Check content length (should be substantial)
        if len(content) > 5000:  # 5KB of text content
            score += 6.0
        elif len(content) > 2000:
            score += 3.0
        else:
            issues.append("Content appears to be too brief")
        
        return score, issues
    
    def get_validation_summary(self, validation_result: ValidationResult) -> str:
        """Generate a human-readable validation summary"""
        summary = []
        
        # Overall status
        status = "✅ VALID" if validation_result.is_valid else "❌ INVALID"
        summary.append(f"Status: {status}")
        summary.append(f"Quality Score: {validation_result.quality_score:.1f}/100")
        summary.append(f"File Size: {validation_result.file_size_bytes:,} bytes")
        summary.append(f"Match Count: {validation_result.match_count}")
        
        # Issues
        if validation_result.issues:
            summary.append("\n🚨 Issues:")
            for issue in validation_result.issues:
                summary.append(f"  • {issue}")
        
        # Warnings
        if validation_result.warnings:
            summary.append("\n⚠️ Warnings:")
            for warning in validation_result.warnings[:5]:  # Limit to 5 warnings
                summary.append(f"  • {warning}")
            if len(validation_result.warnings) > 5:
                summary.append(f"  • ... and {len(validation_result.warnings) - 5} more")
        
        return "\n".join(summary)


# Global validator instance
data_quality_validator = PlayerDataQualityValidator()
