#!/usr/bin/env python3
"""
Check current balance configuration and version
"""

import json
from pathlib import Path
from datetime import datetime

def check_current_balance_config():
    """Check the current balance configuration and version"""
    print("📊 Current Balance Configuration Status")
    print("=" * 60)
    
    # Check historical_momentum_balance.json
    balance_file = Path("enhanced_learning_data") / "historical_momentum_balance.json"
    if balance_file.exists():
        try:
            with open(balance_file, 'r') as f:
                balance_config = json.load(f)
            
            print("📁 Historical Momentum Balance Configuration:")
            print(f"   Version: {balance_config.get('version', 'Unknown')}")
            print(f"   Created: {balance_config.get('created_at', 'Unknown')}")
            print(f"   Accuracy Score: {balance_config.get('accuracy_score', 0):.1%}")
            print(f"   Sample Size: {balance_config.get('sample_size', 0)}")
            
            print("\n🎯 Set-Specific Balance Ratios:")
            for set_num in range(1, 6):
                set_key = f"set_{set_num}_balance"
                if set_key in balance_config:
                    set_balance = balance_config[set_key]
                    hist = set_balance.get('historical', 0)
                    mom = set_balance.get('momentum', 0)
                    print(f"   Set {set_num}: {hist:.1%} historical / {mom:.1%} momentum")
            
            print("\n📈 Game Stage Balance Ratios:")
            stages = [
                ('early_games_balance', 'Early Games (0-4)'),
                ('mid_games_balance', 'Mid Games (5-10)'),
                ('late_games_balance', 'Late Games (11+)')
            ]
            for stage_key, stage_name in stages:
                if stage_key in balance_config:
                    stage_balance = balance_config[stage_key]
                    hist = stage_balance.get('historical', 0)
                    mom = stage_balance.get('momentum', 0)
                    print(f"   {stage_name}: {hist:.1%} historical / {mom:.1%} momentum")
            
            print("\n🏟️ Surface Adjustments:")
            surfaces = ['clay_adjustment', 'hard_adjustment', 'grass_adjustment']
            for surface in surfaces:
                if surface in balance_config:
                    adj = balance_config[surface]
                    surface_name = surface.replace('_adjustment', '').title()
                    print(f"   {surface_name}: {adj:+.1%} adjustment to historical weight")
            
        except Exception as e:
            print(f"❌ Error reading balance config: {e}")
    else:
        print("❌ No balance configuration file found")
    
    # Check unified validation config
    unified_file = Path("enhanced_learning_data") / "unified_validation_config.json"
    if unified_file.exists():
        try:
            with open(unified_file, 'r') as f:
                unified_config = json.load(f)
            
            print(f"\n🔬 Unified Validation Configuration:")
            print(f"   Applied At: {unified_config.get('applied_at', 'Unknown')}")
            print(f"   Constrained: {unified_config.get('constrained', False)}")
            
            balance_changes = unified_config.get('balance_ratios', {})
            if balance_changes:
                print(f"\n📊 Applied Balance Changes:")
                hist = balance_changes.get('historical_weight', 0)
                mom = balance_changes.get('momentum_weight', 0)
                print(f"   Historical Weight: {hist:.1%}")
                print(f"   Momentum Weight: {mom:.1%}")
            
            weight_constraints = unified_config.get('weight_constraints', {})
            if weight_constraints:
                print(f"\n⚖️ Applied Weight Constraints:")
                max_boost = weight_constraints.get('max_individual_weight_boost', 0)
                eff_hist = weight_constraints.get('effective_historical_ratio', 0)
                eff_mom = weight_constraints.get('effective_momentum_ratio', 0)
                print(f"   Max Individual Weight Boost: {max_boost:.1%}")
                print(f"   Effective Historical Ratio: {eff_hist:.1%}")
                print(f"   Effective Momentum Ratio: {eff_mom:.1%}")
            
        except Exception as e:
            print(f"❌ Error reading unified config: {e}")
    else:
        print("\n📝 No unified validation configuration found (not applied yet)")

def show_what_happens_when_applying():
    """Explain what happens when applying unified optimization"""
    print("\n" + "=" * 60)
    print("🔧 What Happens When You Apply Unified Optimization?")
    print("=" * 60)
    
    print("\n1️⃣ **Balance Configuration Update**:")
    print("   • Updates historical_momentum_balance.json")
    print("   • Increments version number (e.g., 2.4 → 2.5)")
    print("   • Sets new created_at timestamp")
    print("   • Updates accuracy_score and sample_size")
    
    print("\n2️⃣ **New Unified Configuration**:")
    print("   • Creates/updates unified_validation_config.json")
    print("   • Stores the optimal balance ratios")
    print("   • Sets weight boost constraints")
    print("   • Records effective ratios after constraints")
    
    print("\n3️⃣ **System Integration**:")
    print("   • Future predictions use the new balance ratios")
    print("   • Individual weight adjustments are constrained")
    print("   • Effective ratios stay within 15% of intended ratios")
    print("   • No more 60/40 → 83/17 skewing!")
    
    print("\n4️⃣ **Monitoring & Tracking**:")
    print("   • All changes are logged with timestamps")
    print("   • Previous configurations are preserved")
    print("   • You can track performance improvements")
    print("   • System learns from the new configuration")
    
    print("\n5️⃣ **Safety Features**:")
    print("   • 24-hour cooldown prevents frequent changes")
    print("   • Statistical significance required for changes")
    print("   • Constraints prevent extreme configurations")
    print("   • Rollback possible if needed")

def clear_cooldown_for_testing():
    """Clear the cooldown for testing purposes"""
    print("\n" + "=" * 60)
    print("🧪 Clearing Cooldown for Testing")
    print("=" * 60)
    
    try:
        from enhanced_adaptive_learning_system import get_global_coordinator
        
        coordinator = get_global_coordinator()
        
        # Show current operations
        recent_ops = coordinator.operation_history[-5:] if coordinator.operation_history else []
        if recent_ops:
            print("📋 Recent Operations:")
            for op in recent_ops:
                print(f"   • {op.operation_type} at {op.requested_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Clear operation history temporarily
        original_count = len(coordinator.operation_history)
        coordinator.operation_history = []
        
        print(f"\n✅ Cleared {original_count} operations from history")
        print("⚠️ This is for testing purposes only!")
        print("💡 You can now run unified validation through the dashboard")
        
        return True
        
    except Exception as e:
        print(f"❌ Error clearing cooldown: {e}")
        return False

def main():
    """Main function"""
    check_current_balance_config()
    show_what_happens_when_applying()
    
    print("\n" + "=" * 60)
    print("🛠️ Testing Options")
    print("=" * 60)
    
    choice = input("\nDo you want to clear the cooldown for testing? (y/n): ").lower().strip()
    if choice == 'y':
        success = clear_cooldown_for_testing()
        if success:
            print("\n🎉 Cooldown cleared! You can now run unified validation in the dashboard.")
        else:
            print("\n❌ Failed to clear cooldown.")
    else:
        print("\n💡 To test unified validation, you can:")
        print("   • Use test_unified_validation.py (bypasses cooldown)")
        print("   • Wait for the 24-hour cooldown to expire")
        print("   • Run this script again and choose 'y' to clear cooldown")

if __name__ == "__main__":
    main()
