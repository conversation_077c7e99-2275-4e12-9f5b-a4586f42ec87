#!/usr/bin/env python3
"""
Test script for the new Unified Validation System
This script allows testing with minimal data requirements for debugging purposes
"""

import sys
import json
from pathlib import Path
from enhanced_adaptive_learning_system import (
    run_unified_validation, 
    apply_unified_optimization,
    get_coordination_status,
    enhanced_learning_system
)

def lower_minimum_requirements_for_testing():
    """
    Temporarily lower minimum prediction requirements for testing purposes
    """
    print("🔧 Lowering minimum requirements for testing...")
    
    # Lower requirements in the unified validator
    if hasattr(enhanced_learning_system.coordinator, 'unified_validator'):
        enhanced_learning_system.coordinator.unified_validator.min_sample_size = 10
        print("   ✅ Unified validator minimum sample size: 10")
    
    # Lower requirements in the coordinator config
    if hasattr(enhanced_learning_system.coordinator, 'config'):
        enhanced_learning_system.coordinator.config.min_balance_validation_samples = 10
        enhanced_learning_system.coordinator.config.min_weight_validation_samples = 10
        print("   ✅ Coordinator minimum samples: 10")
    
    # Lower requirements in the enhanced learning system
    if hasattr(enhanced_learning_system, 'coordinator'):
        enhanced_learning_system.coordinator.config.min_balance_validation_samples = 10
        enhanced_learning_system.coordinator.config.min_weight_validation_samples = 10
        print("   ✅ Enhanced learning system minimum samples: 10")

def restore_normal_requirements():
    """
    Restore normal minimum prediction requirements after testing
    """
    print("🔄 Restoring normal requirements...")
    
    # Restore requirements in the unified validator
    if hasattr(enhanced_learning_system.coordinator, 'unified_validator'):
        enhanced_learning_system.coordinator.unified_validator.min_sample_size = 150
        print("   ✅ Unified validator minimum sample size: 150")
    
    # Restore requirements in the coordinator config
    if hasattr(enhanced_learning_system.coordinator, 'config'):
        enhanced_learning_system.coordinator.config.min_balance_validation_samples = 150
        enhanced_learning_system.coordinator.config.min_weight_validation_samples = 100
        print("   ✅ Coordinator minimum samples restored")

def test_unified_validation():
    """
    Test the unified validation system
    """
    print("🧪 Testing Unified Validation System")
    print("=" * 60)
    
    # Step 1: Check current coordination status
    print("\n📊 Current Coordination Status:")
    status = get_coordination_status()
    print(f"   Current state: {status['current_state']}")
    print(f"   Can run unified validation: {status.get('can_run_unified_validation', 'Unknown')}")
    print(f"   Recent operations: {len(status.get('recent_operations', []))}")
    
    # Step 2: Check available predictions
    learning_predictions = enhanced_learning_system.get_learning_eligible_predictions()
    print(f"\n📈 Available Learning Data:")
    print(f"   Learning-eligible predictions: {len(learning_predictions)}")
    
    if len(learning_predictions) < 10:
        print("❌ Insufficient data for testing. Need at least 10 predictions.")
        print("💡 Please record some predictions first using enhanced_gui.py")
        return False
    
    # Step 3: Lower requirements for testing
    lower_minimum_requirements_for_testing()
    
    try:
        # Step 4: Run unified validation
        print("\n🔬 Running Unified Validation...")
        validation_results = run_unified_validation()
        
        print(f"\n📋 Validation Results:")
        print(f"   Status: {validation_results.get('status', 'Unknown')}")
        
        if validation_results.get('status') == 'success':
            print("✅ Unified validation completed successfully!")
            
            # Show current effective ratios
            current_ratios = validation_results.get('current_effective_ratios', {})
            print(f"\n📊 Current Effective Ratios:")
            for context, ratios in current_ratios.items():
                print(f"   {context}:")
                print(f"     Intended: {ratios.get('intended_historical', 0):.1%} hist / {ratios.get('intended_momentum', 0):.1%} mom")
                print(f"     Effective: {ratios.get('effective_historical', 0):.1%} hist / {ratios.get('effective_momentum', 0):.1%} mom")
                print(f"     Deviation: {ratios.get('deviation', 0):.1%}")
            
            # Show optimization results
            optimization = validation_results.get('optimization_results', {})
            best_combo = optimization.get('best_combination')
            if best_combo:
                print(f"\n🎯 Best Combination Found:")
                print(f"   Intended Balance: {best_combo.get('intended_historical', 0):.1%} hist / {best_combo.get('intended_momentum', 0):.1%} mom")
                print(f"   Weight Boost: {best_combo.get('weight_boost', 0):.1%}")
                print(f"   Effective Balance: {best_combo.get('effective_historical', 0):.1%} hist / {best_combo.get('effective_momentum', 0):.1%} mom")
                print(f"   Simulated Accuracy: {best_combo.get('simulated_accuracy', 0):.1%}")
                print(f"   Constrained: {best_combo.get('constrained', False)}")
            
            # Show recommendations
            recommendations = validation_results.get('recommendations', [])
            if recommendations:
                print(f"\n💡 Recommendations:")
                for rec in recommendations:
                    print(f"   {rec}")
            
            # Ask if user wants to apply the optimization
            if validation_results.get('validation_results', {}).get('has_improvements', False):
                apply_choice = input("\n❓ Apply the unified optimization? (y/n): ").lower().strip()
                if apply_choice == 'y':
                    print("\n🔧 Applying unified optimization...")
                    application_results = apply_unified_optimization(validation_results.get('optimization_results', {}))
                    
                    if application_results.get('status') == 'success':
                        print("✅ Unified optimization applied successfully!")
                        print(f"📊 Balance changes: {application_results.get('balance_changes', {})}")
                        print(f"⚖️ Weight constraints: {application_results.get('weight_constraints', {})}")
                    else:
                        print(f"❌ Failed to apply optimization: {application_results.get('message', 'Unknown error')}")
                else:
                    print("⏸️ Optimization not applied")
            
        elif validation_results.get('status') == 'insufficient_data':
            print(f"❌ Insufficient data: {validation_results.get('message', 'Unknown reason')}")
            print(f"   Sample size: {validation_results.get('sample_size', 0)}")
            print(f"   Required: {validation_results.get('required_size', 150)}")
            
        else:
            print(f"❌ Validation failed: {validation_results.get('message', 'Unknown error')}")
        
        return validation_results.get('status') == 'success'
        
    except Exception as e:
        print(f"❌ Error during unified validation: {e}")
        return False
    
    finally:
        # Step 5: Restore normal requirements
        restore_normal_requirements()

def main():
    """Main test function"""
    print("🔬 Unified Validation System Test")
    print("This script tests the new unified validation system that prevents")
    print("conflicts between balance ratios and individual weight adjustments.")
    print()
    
    success = test_unified_validation()
    
    if success:
        print("\n🎉 Unified validation test completed successfully!")
        print("💡 You can now use run_unified_validation() instead of separate balance/weight validations")
    else:
        print("\n⚠️ Unified validation test encountered issues")
        print("💡 Please check the error messages above and ensure you have sufficient prediction data")

if __name__ == "__main__":
    main()
