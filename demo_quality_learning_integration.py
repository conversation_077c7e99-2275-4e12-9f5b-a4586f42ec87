"""
Demonstration: Quality System Integration with Learning & Validation
Shows how quality affects learning decisions, balance optimization, and validation
"""

from datetime import datetime
from typing import List, Dict, Any
import json

def simulate_prediction_quality_scenarios():
    """Simulate different quality scenarios and their impact on learning"""
    print("🧠 QUALITY IMPACT ON LEARNING DECISIONS")
    print("=" * 80)
    
    # Simulate different quality scenarios
    scenarios = [
        {
            "name": "High Quality Dataset",
            "description": "Mostly high-confidence predictions with good data",
            "predictions": [
                {"confidence": 0.85, "correct": True, "reliability": "high"},
                {"confidence": 0.82, "correct": True, "reliability": "high"},
                {"confidence": 0.78, "correct": False, "reliability": "high"},
                {"confidence": 0.80, "correct": True, "reliability": "high"},
                {"confidence": 0.75, "correct": True, "reliability": "medium"},
                {"confidence": 0.73, "correct": True, "reliability": "medium"},
                {"confidence": 0.70, "correct": False, "reliability": "medium"},
                {"confidence": 0.68, "correct": True, "reliability": "medium"}
            ]
        },
        {
            "name": "Mixed Quality Dataset", 
            "description": "Mix of high, medium, and low quality predictions",
            "predictions": [
                {"confidence": 0.85, "correct": True, "reliability": "high"},
                {"confidence": 0.75, "correct": True, "reliability": "medium"},
                {"confidence": 0.45, "correct": False, "reliability": "low"},
                {"confidence": 0.80, "correct": True, "reliability": "high"},
                {"confidence": 0.35, "correct": False, "reliability": "low"},
                {"confidence": 0.65, "correct": True, "reliability": "medium"},
                {"confidence": 0.25, "correct": False, "reliability": "low"},
                {"confidence": 0.70, "correct": True, "reliability": "medium"}
            ]
        },
        {
            "name": "Low Quality Dataset",
            "description": "Mostly low-confidence predictions with poor data",
            "predictions": [
                {"confidence": 0.45, "correct": True, "reliability": "low"},
                {"confidence": 0.35, "correct": False, "reliability": "low"},
                {"confidence": 0.40, "correct": False, "reliability": "low"},
                {"confidence": 0.55, "correct": True, "reliability": "medium"},
                {"confidence": 0.30, "correct": False, "reliability": "low"},
                {"confidence": 0.42, "correct": True, "reliability": "low"},
                {"confidence": 0.38, "correct": False, "reliability": "low"},
                {"confidence": 0.48, "correct": True, "reliability": "low"}
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print(f"   {scenario['description']}")
        print("   " + "-" * 70)
        
        predictions = scenario['predictions']
        
        # Calculate traditional accuracy
        traditional_accuracy = sum(p['correct'] for p in predictions) / len(predictions)
        
        # Calculate quality-weighted accuracy
        weighted_correct = sum(p['confidence'] * (1 if p['correct'] else 0) for p in predictions)
        total_weight = sum(p['confidence'] for p in predictions)
        quality_weighted_accuracy = weighted_correct / total_weight
        
        # Calculate learning eligibility
        high_quality = [p for p in predictions if p['reliability'] == 'high']
        medium_quality = [p for p in predictions if p['reliability'] == 'medium']
        low_quality = [p for p in predictions if p['reliability'] == 'low']
        
        # Learning decision logic
        if len(high_quality) >= 3:
            learning_decision = "AGGRESSIVE - Use high-quality data for rapid learning"
            learning_sample = high_quality
        elif len(high_quality) + len(medium_quality) >= 5:
            learning_decision = "CONSERVATIVE - Use high+medium quality data"
            learning_sample = high_quality + medium_quality
        else:
            learning_decision = "CAUTIOUS - Insufficient quality data, minimal learning"
            learning_sample = predictions  # Use all with quality weighting
        
        print(f"   Traditional Accuracy: {traditional_accuracy:.1%}")
        print(f"   Quality-Weighted Accuracy: {quality_weighted_accuracy:.1%}")
        print(f"   Quality Distribution: {len(high_quality)}H / {len(medium_quality)}M / {len(low_quality)}L")
        print(f"   Learning Decision: {learning_decision}")
        print(f"   Learning Sample Size: {len(learning_sample)} predictions")
        
        # Show impact on balance optimization
        if len(learning_sample) >= 3:
            sample_accuracy = sum(p['correct'] for p in learning_sample) / len(learning_sample)
            sample_confidence = sum(p['confidence'] for p in learning_sample) / len(learning_sample)
            combined_score = sample_accuracy + (sample_confidence * 0.05)
            print(f"   Balance Optimization Score: {combined_score:.3f}")
        else:
            print(f"   Balance Optimization: SKIPPED (insufficient data)")

def demonstrate_validation_filtering():
    """Show how quality affects validation filtering"""
    print("\n\n🔍 QUALITY IMPACT ON VALIDATION FILTERING")
    print("=" * 80)
    
    # Simulate a realistic prediction dataset
    all_predictions = [
        {"id": "pred_001", "confidence": 0.85, "reliability": "high", "complete": True},
        {"id": "pred_002", "confidence": 0.75, "reliability": "medium", "complete": True},
        {"id": "pred_003", "confidence": 0.35, "reliability": "low", "complete": True},
        {"id": "pred_004", "confidence": 0.80, "reliability": "high", "complete": True},
        {"id": "pred_005", "confidence": 0.25, "reliability": "low", "complete": False},  # Incomplete
        {"id": "pred_006", "confidence": 0.65, "reliability": "medium", "complete": True},
        {"id": "pred_007", "confidence": 0.15, "reliability": "low", "complete": True},
        {"id": "pred_008", "confidence": 0.70, "reliability": "medium", "complete": True},
        {"id": "pred_009", "confidence": 0.40, "reliability": "low", "complete": True},
        {"id": "pred_010", "confidence": 0.88, "reliability": "high", "complete": True}
    ]
    
    print(f"Total Predictions Available: {len(all_predictions)}")
    
    # Different validation filtering strategies
    strategies = [
        {
            "name": "No Quality Filtering",
            "description": "Traditional approach - use all complete predictions",
            "filter": lambda p: p['complete']
        },
        {
            "name": "Basic Quality Filtering", 
            "description": "Exclude low-reliability predictions",
            "filter": lambda p: p['complete'] and p['reliability'] != 'low'
        },
        {
            "name": "Confidence Threshold",
            "description": "Require minimum 0.5 confidence",
            "filter": lambda p: p['complete'] and p['confidence'] >= 0.5
        },
        {
            "name": "High Quality Only",
            "description": "Only high-reliability predictions",
            "filter": lambda p: p['complete'] and p['reliability'] == 'high'
        }
    ]
    
    for strategy in strategies:
        filtered = [p for p in all_predictions if strategy['filter'](p)]
        avg_confidence = sum(p['confidence'] for p in filtered) / len(filtered) if filtered else 0
        
        print(f"\n   {strategy['name']}:")
        print(f"      {strategy['description']}")
        print(f"      Predictions Used: {len(filtered)}/{len(all_predictions)}")
        print(f"      Average Confidence: {avg_confidence:.3f}")
        
        # Reliability distribution
        if filtered:
            high_count = sum(1 for p in filtered if p['reliability'] == 'high')
            medium_count = sum(1 for p in filtered if p['reliability'] == 'medium')
            low_count = sum(1 for p in filtered if p['reliability'] == 'low')
            print(f"      Quality Mix: {high_count}H / {medium_count}M / {low_count}L")
            
            # Validation reliability assessment
            if avg_confidence >= 0.7:
                validation_quality = "HIGH - Reliable validation results expected"
            elif avg_confidence >= 0.5:
                validation_quality = "MEDIUM - Reasonable validation reliability"
            else:
                validation_quality = "LOW - Validation results may be unreliable"
            print(f"      Validation Quality: {validation_quality}")

def show_balance_optimization_impact():
    """Demonstrate quality impact on balance optimization"""
    print("\n\n⚖️ QUALITY IMPACT ON BALANCE OPTIMIZATION")
    print("=" * 80)
    
    # Simulate balance testing scenarios
    balance_ratios = [(0.3, 0.7), (0.5, 0.5), (0.7, 0.3)]
    
    # Different quality datasets for each balance ratio
    test_data = {
        (0.3, 0.7): [  # Historical 30%, Momentum 70%
            {"correct": True, "confidence": 0.85, "quality_weight": 1.35},
            {"correct": True, "confidence": 0.75, "quality_weight": 1.25},
            {"correct": False, "confidence": 0.45, "quality_weight": 0.95},
            {"correct": True, "confidence": 0.80, "quality_weight": 1.30}
        ],
        (0.5, 0.5): [  # Historical 50%, Momentum 50%
            {"correct": True, "confidence": 0.70, "quality_weight": 1.20},
            {"correct": True, "confidence": 0.65, "quality_weight": 1.15},
            {"correct": True, "confidence": 0.75, "quality_weight": 1.25},
            {"correct": False, "confidence": 0.60, "quality_weight": 1.10}
        ],
        (0.7, 0.3): [  # Historical 70%, Momentum 30%
            {"correct": False, "confidence": 0.55, "quality_weight": 1.05},
            {"correct": True, "confidence": 0.50, "quality_weight": 1.00},
            {"correct": False, "confidence": 0.45, "quality_weight": 0.95},
            {"correct": True, "confidence": 0.60, "quality_weight": 1.10}
        ]
    }
    
    print("Testing different balance ratios with quality weighting:\n")
    
    best_ratio = None
    best_score = 0.0
    
    for hist_ratio, mom_ratio in balance_ratios:
        predictions = test_data[(hist_ratio, mom_ratio)]
        
        # Traditional accuracy calculation
        traditional_accuracy = sum(p['correct'] for p in predictions) / len(predictions)
        
        # Quality-weighted accuracy calculation
        weighted_correct = sum(p['quality_weight'] * (1 if p['correct'] else 0) for p in predictions)
        total_weight = sum(p['quality_weight'] for p in predictions)
        quality_weighted_accuracy = weighted_correct / total_weight
        
        # Average confidence
        avg_confidence = sum(p['confidence'] for p in predictions) / len(predictions)
        
        # Combined score (as used in actual system)
        if quality_weighted_accuracy >= 0.99:
            combined_score = quality_weighted_accuracy + (avg_confidence * 0.1)
        else:
            combined_score = quality_weighted_accuracy + (avg_confidence * 0.05)
        
        print(f"   Balance Ratio {hist_ratio:.0%}H / {mom_ratio:.0%}M:")
        print(f"      Traditional Accuracy: {traditional_accuracy:.1%}")
        print(f"      Quality-Weighted Accuracy: {quality_weighted_accuracy:.1%}")
        print(f"      Average Confidence: {avg_confidence:.3f}")
        print(f"      Combined Score: {combined_score:.3f}")
        
        if combined_score > best_score:
            best_score = combined_score
            best_ratio = (hist_ratio, mom_ratio)
        
        print()
    
    print(f"🏆 Optimal Balance Ratio: {best_ratio[0]:.0%}H / {best_ratio[1]:.0%}M")
    print(f"   Best Combined Score: {best_score:.3f}")
    print(f"   Quality Impact: Higher confidence predictions drive optimization decisions")

def demonstrate_learning_thresholds():
    """Show how quality affects learning threshold decisions"""
    print("\n\n📊 QUALITY-BASED LEARNING THRESHOLDS")
    print("=" * 80)
    
    contexts = [
        {
            "name": "Clay Court",
            "base_requirement": 25,
            "available_predictions": 30,
            "quality_distribution": {"high": 8, "medium": 12, "low": 10}
        },
        {
            "name": "Hard Court",
            "base_requirement": 25, 
            "available_predictions": 45,
            "quality_distribution": {"high": 15, "medium": 20, "low": 10}
        },
        {
            "name": "Grass Court",
            "base_requirement": 25,
            "available_predictions": 18,
            "quality_distribution": {"high": 3, "medium": 8, "low": 7}
        }
    ]
    
    for context in contexts:
        print(f"\n   {context['name']} Context:")
        print(f"      Base Requirement: {context['base_requirement']} predictions")
        print(f"      Available: {context['available_predictions']} predictions")
        
        quality_dist = context['quality_distribution']
        print(f"      Quality Mix: {quality_dist['high']}H / {quality_dist['medium']}M / {quality_dist['low']}L")
        
        # Calculate quality-adjusted requirement
        high_quality_ratio = quality_dist['high'] / context['available_predictions']
        medium_quality_ratio = quality_dist['medium'] / context['available_predictions']
        
        if high_quality_ratio >= 0.3:  # 30%+ high quality
            adjusted_requirement = context['base_requirement']
            learning_status = "READY - Sufficient high-quality data"
        elif high_quality_ratio + medium_quality_ratio >= 0.6:  # 60%+ high+medium quality
            adjusted_requirement = int(context['base_requirement'] * 1.1)
            learning_status = "READY - Good quality data available"
        else:  # Mostly low quality
            adjusted_requirement = int(context['base_requirement'] * 1.3)
            learning_status = "CAUTIOUS - Low quality data, need more samples"
        
        print(f"      Adjusted Requirement: {adjusted_requirement} predictions")
        print(f"      Learning Status: {learning_status}")
        
        if context['available_predictions'] >= adjusted_requirement:
            print(f"      ✅ LEARNING ENABLED")
        else:
            needed = adjusted_requirement - context['available_predictions']
            print(f"      ❌ LEARNING DISABLED - Need {needed} more predictions")

def main():
    """Run the complete quality-learning integration demonstration"""
    simulate_prediction_quality_scenarios()
    demonstrate_validation_filtering()
    show_balance_optimization_impact()
    demonstrate_learning_thresholds()
    
    print("\n\n✅ QUALITY-LEARNING INTEGRATION DEMONSTRATION COMPLETED")
    print("=" * 80)
    print("Key Integration Benefits:")
    print("• Quality-weighted accuracy provides more reliable learning signals")
    print("• Quality filtering improves validation reliability and statistical significance")
    print("• Quality-based thresholds prevent learning from poor data")
    print("• Confidence weighting ensures high-quality predictions drive optimization")
    print("• Adaptive learning sensitivity based on data quality availability")
    print("• Transparent quality tracking enables debugging and improvement")
    print("=" * 80)

if __name__ == "__main__":
    main()
