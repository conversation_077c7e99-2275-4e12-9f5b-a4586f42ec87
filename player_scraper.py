import requests
from bs4 import BeautifulSoup
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import re
import json

def extract_js_variables(driver):
    """Extract JavaScript variables from the page"""
    try:
        # Extract player biographical data
        bio_vars = driver.execute_script("""
            return {
                fullname: typeof fullname !== 'undefined' ? fullname : '',
                currentrank: typeof currentrank !== 'undefined' ? currentrank : '',
                peakrank: typeof peakrank !== 'undefined' ? peakrank : '',
                peakfirst: typeof peakfirst !== 'undefined' ? peakfirst : '',
                peaklast: typeof peaklast !== 'undefined' ? peaklast : '',
                dob: typeof dob !== 'undefined' ? dob : '',
                ht: typeof ht !== 'undefined' ? ht : '',
                hand: typeof hand !== 'undefined' ? hand : '',
                backhand: typeof backhand !== 'undefined' ? backhand : '',
                country: typeof country !== 'undefined' ? country : '',
                elo_rating: typeof elo_rating !== 'undefined' ? elo_rating : '',
                elo_rank: typeof elo_rank !== 'undefined' ? elo_rank : '',
                twitter: typeof twitter !== 'undefined' ? twitter : '',
                atp_id: typeof atp_id !== 'undefined' ? atp_id : '',
                itf_id: typeof itf_id !== 'undefined' ? itf_id : ''
            };
        """)

        # Extract match data
        match_data = driver.execute_script("""
            if (typeof matchmx !== 'undefined' && typeof matchhead !== 'undefined') {
                return {
                    matchhead: matchhead,
                    matchmx: matchmx
                };
            }
            return null;
        """)

        return bio_vars, match_data
    except Exception as e:
        print(f"Error extracting JS variables: {e}")
        return {}, None

def format_date(date_str):
    """Convert YYYYMMDD format to readable date"""
    if not date_str or len(date_str) != 8:
        return date_str
    try:
        year = date_str[:4]
        month = date_str[4:6]
        day = date_str[6:8]
        return f"{day}-{month}-{year}"
    except:
        return date_str

def calculate_percentages(match_data):
    """Calculate percentage statistics from raw match data"""
    def safe_percentage(numerator, denominator):
        """Safely calculate percentage, return empty string if invalid"""
        try:
            num = float(numerator) if numerator else 0
            den = float(denominator) if denominator else 0
            if den == 0:
                return ""
            return f"{(num/den*100):.1f}%"
        except:
            return ""

    def safe_float(value):
        """Safely convert to float"""
        try:
            return float(value) if value else 0
        except:
            return 0

    # Calculate player percentages
    sp = safe_float(match_data.get('SP', 0))
    first_serve_won = safe_float(match_data.get('1SP', 0))
    second_serve_won = safe_float(match_data.get('2SP', 0))
    bp_saved = safe_float(match_data.get('BP Saved', 0))
    bp_faced = safe_float(match_data.get('BP Faced', 0))
    aces = safe_float(match_data.get('Aces', 0))
    dfs = safe_float(match_data.get('DFs', 0))

    # Calculate opponent percentages
    opp_sp = safe_float(match_data.get('Opp SP', 0))
    opp_first_serve_won = safe_float(match_data.get('Opp 1SP', 0))
    opp_second_serve_won = safe_float(match_data.get('Opp 2SP', 0))
    opp_bp_saved = safe_float(match_data.get('Opp BP Saved', 0))
    opp_bp_faced = safe_float(match_data.get('Opp BP Faced', 0))

    # Calculate percentages
    percentages = {}

    # Service percentages
    if sp > 0:
        percentages['Service Points Won %'] = safe_percentage(first_serve_won + second_serve_won, sp)
        percentages['1st Serve Win %'] = safe_percentage(first_serve_won, sp * 0.6)  # Estimate 60% first serves
        percentages['2nd Serve Win %'] = safe_percentage(second_serve_won, sp * 0.4)  # Estimate 40% second serves
        percentages['Ace %'] = safe_percentage(aces, sp)
        percentages['Double Fault %'] = safe_percentage(dfs, sp)

    # Break point percentages
    percentages['BP Saved %'] = safe_percentage(bp_saved, bp_faced)

    # Opponent percentages
    if opp_sp > 0:
        percentages['Opp Service Points Won %'] = safe_percentage(opp_first_serve_won + opp_second_serve_won, opp_sp)
        percentages['Return Points Won %'] = safe_percentage(opp_sp - (opp_first_serve_won + opp_second_serve_won), opp_sp)

    percentages['Opp BP Saved %'] = safe_percentage(opp_bp_saved, opp_bp_faced)
    percentages['BP Conversion %'] = safe_percentage(opp_bp_faced - opp_bp_saved, opp_bp_faced)

    return percentages

def clean_match_data(matches):
    """Clean up match data by removing menu rows and invalid entries"""
    cleaned = []
    for match in matches:
        # Skip menu rows and invalid data
        if (match.get('Tournament', '').lower() in ['show career', 'custom', 'all'] or
            not match.get('Date', '').strip() or
            match.get('Date', '').isdigit()):  # Skip rows where date is just a year
            continue
        cleaned.append(match)
    return cleaned

def get_player_data(url, fast_mode=False):
    try:
        print("Setting up Chrome driver...")
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # Run in headless mode
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')  # Disable GPU hardware acceleration
        chrome_options.add_argument('--window-size=1920,1080')  # Set a standard window size

        # Performance optimizations for bulk downloads
        if fast_mode:
            chrome_options.add_argument('--disable-images')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-ipc-flooding-protection')
            # Note: We keep JavaScript enabled as Tennis Abstract needs it for data

        driver = webdriver.Chrome(options=chrome_options)

        # Adjust wait times based on mode
        wait_time = 10 if fast_mode else 20
        js_wait = 2 if fast_mode else 5
        page_wait = 5 if fast_mode else 15

        wait = WebDriverWait(driver, wait_time)

        print("Fetching URL...")
        driver.get(url)

        # Wait for the main content to load
        try:
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            print("Page loaded successfully")
        except:
            print("Waiting longer for page to load...")
            time.sleep(page_wait)

        # Wait for JavaScript variables to load
        print("Waiting for JavaScript variables to load...")
        time.sleep(js_wait)

        # Extract data from JavaScript variables
        bio_vars, match_data = extract_js_variables(driver)

        # Initialize player info with biographical data
        player_info = {'matches': []}

        # Process biographical information
        if bio_vars:
            player_info['name'] = bio_vars.get('fullname', '')
            player_info['country'] = bio_vars.get('country', '')
            player_info['Current rank'] = str(bio_vars.get('currentrank', ''))

            # Format peak rank with date
            peak_rank = bio_vars.get('peakrank', '')
            peak_first = bio_vars.get('peakfirst', '')
            if peak_rank and peak_first:
                peak_date = format_date(str(peak_first))
                player_info['Peak rank'] = f"{peak_rank} ({peak_date})"
            elif peak_rank:
                player_info['Peak rank'] = str(peak_rank)

            # Format Elo rank with rating
            elo_rank = bio_vars.get('elo_rank', '')
            elo_rating = bio_vars.get('elo_rating', '')
            if elo_rank and elo_rating:
                player_info['Elo rank'] = f"{elo_rank} (rating: {elo_rating})"
            elif elo_rank:
                player_info['Elo rank'] = str(elo_rank)

            # Format date of birth and calculate age
            dob = bio_vars.get('dob', '')
            if dob and len(str(dob)) == 8:
                dob_str = str(dob)
                formatted_dob = format_date(dob_str)
                try:
                    birth_date = datetime.strptime(dob_str, '%Y%m%d')
                    age = (datetime.now() - birth_date).days // 365
                    player_info['Age'] = f"{age} ({formatted_dob})"
                except:
                    player_info['Age'] = formatted_dob

            # Format playing style
            hand = bio_vars.get('hand', '')
            backhand = bio_vars.get('backhand', '')
            if hand and backhand:
                hand_text = 'Right' if hand == 'R' else 'Left'
                backhand_text = 'two-handed backhand' if backhand == '2' else 'one-handed backhand'
                player_info['Plays'] = f"{hand_text} ({backhand_text})"

            # Add height
            height = bio_vars.get('ht', '')
            if height:
                player_info['Height'] = f"{height} cm"

            # Add additional IDs
            if bio_vars.get('atp_id'):
                player_info['ATP ID'] = bio_vars.get('atp_id')
            if bio_vars.get('itf_id'):
                player_info['ITF ID'] = bio_vars.get('itf_id')
            if bio_vars.get('twitter'):
                player_info['Twitter'] = bio_vars.get('twitter')

        # Process match data from JavaScript
        matches = []
        if match_data and match_data.get('matchmx') and match_data.get('matchhead'):
            matchhead = match_data['matchhead']
            matchmx = match_data['matchmx']

            print(f"Found {len(matchmx)} matches in JavaScript data")
            print(f"Match headers: {matchhead}")

            # Process each match
            for match_row in matchmx[:30]:  # Get last 30 matches
                match = {}
                for i, header in enumerate(matchhead):
                    if i < len(match_row):
                        value = match_row[i]

                        # Map JavaScript field names to our field names and format data
                        if header == 'date':
                            match['Date'] = format_date(str(value)) if value else ''
                        elif header == 'tourn':
                            match['Tournament'] = str(value) if value else ''
                        elif header == 'surf':
                            match['Surface'] = str(value) if value else ''
                        elif header == 'round':
                            match['Rd'] = str(value) if value else ''
                        elif header == 'rank':
                            match['Rk'] = str(value) if value else ''
                        elif header == 'orank':
                            match['vRk'] = str(value) if value else ''
                        elif header == 'opp':
                            match['Opponent'] = str(value) if value else ''
                        elif header == 'score':
                            match['Score'] = str(value) if value else ''
                        elif header == 'wl':
                            match['W/L'] = str(value) if value else ''
                        elif header == 'level':
                            match['Level'] = str(value) if value else ''
                        elif header == 'seed':
                            match['Seed'] = str(value) if value else ''
                        elif header == 'entry':
                            match['Entry'] = str(value) if value else ''
                        elif header == 'oseed':
                            match['Opp Seed'] = str(value) if value else ''
                        elif header == 'oentry':
                            match['Opp Entry'] = str(value) if value else ''
                        elif header == 'ohand':
                            hand_map = {'R': 'Right', 'L': 'Left'}
                            match['Opp Hand'] = hand_map.get(str(value), str(value)) if value else ''
                        elif header == 'obday':
                            match['Opp DOB'] = format_date(str(value)) if value else ''
                        elif header == 'oht':
                            match['Opp Height'] = f"{value} cm" if value else ''
                        elif header == 'ocountry':
                            match['Opp Country'] = str(value) if value else ''
                        elif header == 'time':
                            match['Time'] = str(value) if value else ''
                        elif header == 'aces':
                            match['Aces'] = str(value) if value else ''
                        elif header == 'dfs':
                            match['DFs'] = str(value) if value else ''
                        elif header == 'pts':
                            match['TP'] = str(value) if value else ''
                        elif header == 'firsts':
                            match['SP'] = str(value) if value else ''
                        elif header == 'fwon':
                            match['1SP'] = str(value) if value else ''
                        elif header == 'swon':
                            match['2SP'] = str(value) if value else ''
                        elif header == 'games':
                            match['Games Won'] = str(value) if value else ''
                        elif header == 'saved':
                            match['BP Saved'] = str(value) if value else ''
                        elif header == 'chances':
                            match['BP Faced'] = str(value) if value else ''
                        elif header == 'oaces':
                            match['vA'] = str(value) if value else ''
                        elif header == 'odfs':
                            match['Opp DFs'] = str(value) if value else ''
                        elif header == 'opts':
                            match['Opp TP'] = str(value) if value else ''
                        elif header == 'ofirsts':
                            match['Opp SP'] = str(value) if value else ''
                        elif header == 'ofwon':
                            match['Opp 1SP'] = str(value) if value else ''
                        elif header == 'oswon':
                            match['Opp 2SP'] = str(value) if value else ''
                        elif header == 'ogames':
                            match['Opp Games Won'] = str(value) if value else ''
                        elif header == 'osaved':
                            match['Opp BP Saved'] = str(value) if value else ''
                        elif header == 'ochances':
                            match['Opp BP Faced'] = str(value) if value else ''
                        elif header == 'matchid':
                            match['Match ID'] = str(value) if value else ''

                # Calculate percentage statistics for this match
                percentages = calculate_percentages(match)
                match.update(percentages)

                matches.append(match)

        player_info['matches'] = matches

        # Print debug info
        print("\nDebug Info:")
        print("Title:", driver.title)
        print("Bio info keys found:", [k for k in player_info.keys() if k != 'matches'])
        print("Matches found:", len(matches))
        if matches:
            print("First match keys:", list(matches[0].keys()) if matches[0] else "No keys")
            print("Sample match data:", matches[0] if matches else "No matches")

        driver.quit()

        # Validate player info before returning
        if not player_info.get('name'):
            print("Warning: Could not find player name")
            # Try to extract name from URL
            player_param = url.split('p=')[-1].split('&')[0]
            player_name = ' '.join(word.capitalize() for word in player_param.split('_'))
            player_info['name'] = player_name
            print(f"Using name from URL: {player_name}")

        return player_info

    except Exception as e:
        print(f"Error scraping data: {e}")
        import traceback
        print(traceback.format_exc())
        if 'driver' in locals():
            driver.quit()
        return None

def save_player_profile(player_data, output_dir='Players'):
    if not player_data or 'name' not in player_data or not player_data['name'].strip():
        print("No valid player data to save")
        print("Available data:", player_data)
        return False

    os.makedirs(output_dir, exist_ok=True)
    name = player_data['name'].split('[')[0].strip()
    if not name:
        print("Could not determine player name")
        return False

    filename = f"{output_dir}/{name}.txt"

    try:
        with open(filename, 'w', encoding='utf-8') as f:
            # Write header with player name
            f.write("=" * 80 + "\n")
            f.write(f"TENNIS PLAYER PROFILE: {player_data['name'].upper()}\n")
            f.write("=" * 80 + "\n\n")

            # Write biographical information in organized sections
            f.write("BIOGRAPHICAL INFORMATION\n")
            f.write("-" * 40 + "\n")

            bio_sections = {
                'Personal': ['Age', 'Height', 'country'],
                'Playing Style': ['Plays'],
                'Rankings': ['Current rank', 'Peak rank', 'Elo rank'],
                'Identifiers': ['ATP ID', 'ITF ID', 'Twitter']
            }

            for section_name, fields in bio_sections.items():
                section_data = []
                for field in fields:
                    if field in player_data and player_data[field]:
                        section_data.append(f"  {field}: {player_data[field]}")

                if section_data:
                    f.write(f"\n{section_name}:\n")
                    f.write('\n'.join(section_data) + '\n')

            # Write matches section
            matches = player_data.get('matches', [])
            f.write(f"\n\nMATCH HISTORY ({len(matches)} matches)\n")
            f.write("=" * 80 + "\n\n")

            if matches:
                # Organize fields into logical groups
                basic_info = ['Date', 'Tournament', 'Surface', 'Rd', 'W/L', 'Score']
                ranking_info = ['Rk', 'vRk']
                tournament_context = ['Level', 'Seed', 'Entry']
                opponent_info = ['Opponent', 'Opp Seed', 'Opp Entry', 'Opp Hand', 'Opp Height', 'Opp Country']
                match_stats = ['Time', 'TP', 'Aces', 'DFs', 'SP', '1SP', '2SP', 'Games Won']
                bp_stats = ['BP Saved', 'BP Faced']
                opponent_stats = ['vA', 'Opp TP', 'Opp 1SP', 'Opp 2SP', 'Opp Games Won', 'Opp BP Saved', 'Opp BP Faced']
                percentages = ['Service Points Won %', '1st Serve Win %', '2nd Serve Win %', 'BP Saved %',
                              'BP Conversion %', 'Return Points Won %', 'Ace %', 'Double Fault %']

                # Get available fields from first match
                available_fields = list(matches[0].keys())

                # Create organized field groups
                field_groups = [
                    ('BASIC INFO', [f for f in basic_info if f in available_fields]),
                    ('RANKINGS', [f for f in ranking_info if f in available_fields]),
                    ('TOURNAMENT', [f for f in tournament_context if f in available_fields]),
                    ('OPPONENT', [f for f in opponent_info if f in available_fields]),
                    ('MATCH STATS', [f for f in match_stats if f in available_fields]),
                    ('BREAK POINTS', [f for f in bp_stats if f in available_fields]),
                    ('OPPONENT STATS', [f for f in opponent_stats if f in available_fields]),
                    ('PERCENTAGES', [f for f in percentages if f in available_fields])
                ]

                # Write each match in organized format
                for i, match in enumerate(matches, 1):
                    f.write(f"MATCH {i}: {match.get('Tournament', 'Unknown')} - {match.get('Date', 'Unknown Date')}\n")
                    f.write("-" * 60 + "\n")

                    for group_name, fields in field_groups:
                        if fields:  # Only show groups that have data
                            group_data = []
                            for field in fields:
                                value = match.get(field, '')
                                if value:  # Only show fields with values
                                    group_data.append(f"  {field}: {value}")

                            if group_data:
                                f.write(f"\n{group_name}:\n")
                                f.write('\n'.join(group_data) + '\n')

                    f.write("\n" + "=" * 60 + "\n\n")

                # Write summary statistics
                f.write("SUMMARY STATISTICS\n")
                f.write("-" * 40 + "\n")

                # Calculate overall statistics
                total_matches = len(matches)
                wins = sum(1 for m in matches if m.get('W/L') == 'W')
                losses = sum(1 for m in matches if m.get('W/L') == 'L')

                f.write(f"Total Matches: {total_matches}\n")
                f.write(f"Wins: {wins}\n")
                f.write(f"Losses: {losses}\n")
                if total_matches > 0:
                    win_pct = (wins / total_matches) * 100
                    f.write(f"Win Percentage: {win_pct:.1f}%\n")

                # Surface breakdown
                surfaces = {}
                for match in matches:
                    surface = match.get('Surface', 'Unknown')
                    if surface not in surfaces:
                        surfaces[surface] = {'total': 0, 'wins': 0}
                    surfaces[surface]['total'] += 1
                    if match.get('W/L') == 'W':
                        surfaces[surface]['wins'] += 1

                f.write(f"\nSurface Breakdown:\n")
                for surface, stats in surfaces.items():
                    if stats['total'] > 0:
                        surface_win_pct = (stats['wins'] / stats['total']) * 100
                        f.write(f"  {surface}: {stats['wins']}-{stats['total'] - stats['wins']} ({surface_win_pct:.1f}%)\n")

            else:
                f.write("No match data available\n")

        print(f"Successfully saved organized profile to {filename}")
        return True

    except Exception as e:
        print(f"Error saving profile: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == '__main__':
    print("Before running this script, make sure you have installed:")
    print("1. selenium (pip install selenium)")
    print("2. Chrome WebDriver (download from https://sites.google.com/chromium.org/driver/)")
    print("\nMake sure the Chrome WebDriver is in your system PATH")
    
    url = input("\nEnter player URL from Tennis Abstract (e.g., https://www.tennisabstract.com/cgi-bin/player-classic.cgi?p=PierreHuguesHerbert): ")
    print("Scraping player data...")
    player_data = get_player_data(url)
    
    if player_data:
        if save_player_profile(player_data):
            print(f"Successfully saved {player_data['name']}'s profile")
        else:
            print("Failed to save player profile")
    else:
        print("Failed to scrape player data - please check the URL and try again")