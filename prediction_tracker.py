"""
Prediction Tracker for Tennis Set Predictions
Tracks accuracy of predictions at different score scenarios
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
from collections import defaultdict

@dataclass
class PredictionRecord:
    """Record of a single prediction"""
    timestamp: str
    score: Tuple[int, int]
    player1_name: str
    player2_name: str
    player1_code: str
    player2_code: str
    predicted_winner: str
    prediction_probability: float
    confidence: float
    actual_winner: Optional[str] = None
    momentum_factors: Dict = None
    # New fields for enhanced predictions
    set_number: int = 1
    match_format: str = "Bo3"  # Bo3 or Bo5
    favorite: Optional[str] = None  # player code of favorite
    favorite_odds: Optional[float] = None
    surface: Optional[str] = None  # Clay, Hard, Grass
    previous_sets_winner: Optional[List[str]] = None
    # AI prediction fields
    is_ai_prediction: bool = False  # True if this is an AI-based prediction
    ai_analysis_text: Optional[str] = None  # Full AI analysis text
    ai_model_used: Optional[str] = None  # Model used for AI prediction

    # Adaptive learning fields
    prompt_weights: Optional[Dict[str, float]] = None  # Weights used in the prompt
    context_factors: Optional[Dict[str, Any]] = None  # Additional context factors
    learning_metadata: Optional[Dict[str, Any]] = None  # Learning system metadata
    prompt_version: Optional[str] = None  # Version of prompt used
    weight_source: Optional[str] = None  # Source of weights (default, learned, manual)

    # Match status tracking for learning control
    session_id: Optional[str] = None  # ID of the match session this prediction belongs to
    match_status: Optional[str] = None  # "pending", "draft", "completed" - controls learning eligibility
    prediction_id: Optional[str] = None  # Unique ID linking to enhanced learning system
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization with robust error handling"""
        try:
            data = asdict(self)
            # Convert tuple to string for JSON
            data['score'] = f"{self.score[0]}-{self.score[1]}"

            # Handle momentum_factors serialization with error recovery
            if self.momentum_factors:
                try:
                    # Convert any enum values and dataclasses to strings/dicts
                    serializable_factors = {}
                    for key, value in self.momentum_factors.items():
                        try:
                            if hasattr(value, '__dataclass_fields__'):  # It's a dataclass (like ServePattern)
                                # Convert dataclass to dict
                                dc_dict = {}
                                for field_name in value.__dataclass_fields__:
                                    try:
                                        field_value = getattr(value, field_name)
                                        if hasattr(field_value, 'value'):  # It's an enum
                                            dc_dict[field_name] = field_value.value
                                        elif isinstance(field_value, (int, float, str, bool, type(None))):
                                            dc_dict[field_name] = field_value
                                        else:
                                            # Convert complex types to string representation
                                            dc_dict[field_name] = str(field_value)
                                    except Exception:
                                        # Skip problematic fields
                                        continue
                                serializable_factors[key] = dc_dict
                            elif hasattr(value, 'value'):  # It's an enum
                                serializable_factors[key] = value.value
                            elif isinstance(value, dict):  # Nested dict
                                nested_dict = {}
                                for k, v in value.items():
                                    try:
                                        if hasattr(v, '__dataclass_fields__'):  # Dataclass
                                            dc_dict = {}
                                            for field_name in v.__dataclass_fields__:
                                                try:
                                                    field_value = getattr(v, field_name)
                                                    if hasattr(field_value, 'value'):  # It's an enum
                                                        dc_dict[field_name] = field_value.value
                                                    elif isinstance(field_value, (int, float, str, bool, type(None))):
                                                        dc_dict[field_name] = field_value
                                                    else:
                                                        dc_dict[field_name] = str(field_value)
                                                except Exception:
                                                    continue
                                            nested_dict[k] = dc_dict
                                        elif hasattr(v, 'value'):  # It's an enum
                                            nested_dict[k] = v.value
                                        elif isinstance(v, (int, float, str, bool, type(None))):
                                            nested_dict[k] = v
                                        else:
                                            nested_dict[k] = str(v)
                                    except Exception:
                                        # Skip problematic nested values
                                        continue
                                serializable_factors[key] = nested_dict
                            elif isinstance(value, (int, float, str, bool, type(None))):
                                serializable_factors[key] = value
                            else:
                                # Convert complex types to string representation
                                serializable_factors[key] = str(value)
                        except Exception:
                            # Skip problematic momentum factors
                            continue
                    data['momentum_factors'] = serializable_factors
                except Exception:
                    # If momentum_factors serialization fails completely, exclude it
                    data['momentum_factors'] = {}

            # Ensure all values are JSON serializable
            for key, value in data.items():
                if not isinstance(value, (int, float, str, bool, type(None), list, dict)):
                    data[key] = str(value)

            return data

        except Exception as e:
            # Fallback: create minimal serializable record
            print(f"Warning: Failed to serialize prediction record, using minimal data: {e}")
            return {
                'timestamp': str(self.timestamp),
                'score': f"{self.score[0]}-{self.score[1]}",
                'player1_name': str(self.player1_name),
                'player2_name': str(self.player2_name),
                'player1_code': str(self.player1_code),
                'player2_code': str(self.player2_code),
                'predicted_winner': str(self.predicted_winner),
                'prediction_probability': float(self.prediction_probability) if isinstance(self.prediction_probability, (int, float)) else 0.5,
                'confidence': float(self.confidence) if isinstance(self.confidence, (int, float)) else 0.5,
                'actual_winner': str(self.actual_winner) if self.actual_winner else None,
                'momentum_factors': {},
                'set_number': int(self.set_number) if hasattr(self, 'set_number') else 1,
                'match_format': str(getattr(self, 'match_format', 'Bo3')),
                'favorite': str(self.favorite) if hasattr(self, 'favorite') and self.favorite else None,
                'favorite_odds': float(self.favorite_odds) if hasattr(self, 'favorite_odds') and self.favorite_odds else None,
                'surface': str(self.surface) if hasattr(self, 'surface') and self.surface else None,
                'previous_sets_winner': list(self.previous_sets_winner) if hasattr(self, 'previous_sets_winner') and self.previous_sets_winner else None,
                'is_ai_prediction': bool(getattr(self, 'is_ai_prediction', False)),
                'ai_analysis_text': str(self.ai_analysis_text) if hasattr(self, 'ai_analysis_text') and self.ai_analysis_text else None,
                'ai_model_used': str(self.ai_model_used) if hasattr(self, 'ai_model_used') and self.ai_model_used else None
            }
    
    @classmethod
    def from_dict(cls, data: Dict):
        """Create from dictionary"""
        # Convert string score back to tuple
        score_parts = data['score'].split('-')
        data['score'] = (int(score_parts[0]), int(score_parts[1]))

        # Handle backward compatibility for AI prediction fields
        if 'is_ai_prediction' not in data:
            data['is_ai_prediction'] = False
        if 'ai_analysis_text' not in data:
            data['ai_analysis_text'] = None
        if 'ai_model_used' not in data:
            data['ai_model_used'] = None

        return cls(**data)

class PredictionTracker:
    """Tracks and analyzes prediction accuracy"""
    
    def __init__(self, data_file: str = "prediction_history.json"):
        self.data_file = data_file
        self.predictions: List[PredictionRecord] = []
        self.load_data()
    
    def get_historical_accuracy_for_score(self, score: Tuple[int, int]) -> Tuple[float, int]:
        """
        Get historical accuracy for a specific score.
        Returns (accuracy, sample_size)
        """
        score_key = f"{score[0]}-{score[1]}"
        predictions_at_score = [p for p in self.predictions 
                               if p.score == score and p.actual_winner is not None]
        
        if not predictions_at_score:
            return (0.5, 0)  # Default to 50% if no history
        
        correct = sum(1 for p in predictions_at_score if p.predicted_winner == p.actual_winner)
        accuracy = correct / len(predictions_at_score)
        
        return (accuracy, len(predictions_at_score))
    
    def load_data(self):
        """Load prediction history from file with recovery options"""
        self.predictions = []

        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content.strip():  # Only parse if file has content
                        data = json.loads(content)
                        # Validate and load each record
                        for record_data in data:
                            try:
                                record = PredictionRecord.from_dict(record_data)
                                self.predictions.append(record)
                            except Exception as e:
                                print(f"Warning: Skipping corrupted record: {e}")
                                continue
                        print(f"Successfully loaded {len(self.predictions)} prediction records")
                    else:
                        print("Prediction history file is empty, starting fresh")
            except Exception as e:
                print(f"Error loading prediction history: {e}")
                # Try to recover from backup or corrupted file
                if self._attempt_recovery():
                    print("Successfully recovered data from backup")
                else:
                    print("Starting with empty prediction history")
        else:
            print("No prediction history file found, starting fresh")

    def _attempt_recovery(self):
        """Attempt to recover data from backup files or corrupted data"""
        import shutil

        # Try to recover from backup file
        backup_file = f"{self.data_file}.backup"
        if os.path.exists(backup_file):
            try:
                print(f"Attempting to recover from backup: {backup_file}")
                with open(backup_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content.strip():
                        data = json.loads(content)
                        recovered_predictions = []
                        for record_data in data:
                            try:
                                record = PredictionRecord.from_dict(record_data)
                                recovered_predictions.append(record)
                            except Exception as e:
                                print(f"Warning: Skipping corrupted record in backup: {e}")
                                continue

                        if recovered_predictions:
                            self.predictions = recovered_predictions
                            # Save the recovered data as the main file
                            self.save_data()
                            print(f"Recovered {len(recovered_predictions)} records from backup")
                            return True
            except Exception as e:
                print(f"Failed to recover from backup: {e}")

        # Try to recover partial data from corrupted main file
        if os.path.exists(self.data_file):
            try:
                print("Attempting to recover partial data from corrupted file")
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Try to fix common JSON corruption issues
                content = content.strip()
                if content and not content.endswith(']'):
                    # Try to close incomplete JSON array
                    if content.endswith(','):
                        content = content[:-1] + ']'
                    elif not content.endswith('}'):
                        # Find last complete record
                        last_brace = content.rfind('}')
                        if last_brace > 0:
                            content = content[:last_brace + 1] + ']'
                    else:
                        content += ']'

                if content:
                    data = json.loads(content)
                    recovered_predictions = []
                    for record_data in data:
                        try:
                            record = PredictionRecord.from_dict(record_data)
                            recovered_predictions.append(record)
                        except Exception as e:
                            print(f"Warning: Skipping corrupted record: {e}")
                            continue

                    if recovered_predictions:
                        self.predictions = recovered_predictions
                        # Backup the corrupted file before overwriting
                        shutil.move(self.data_file, f"{self.data_file}.corrupted")
                        self.save_data()
                        print(f"Recovered {len(recovered_predictions)} records from corrupted file")
                        return True

            except Exception as e:
                print(f"Failed to recover from corrupted file: {e}")

        return False
    
    def save_data(self):
        """Save prediction history to file with atomic write and error recovery"""
        import tempfile
        import shutil

        try:
            # Prepare data for serialization
            data = []
            for pred in self.predictions:
                try:
                    pred_dict = pred.to_dict()
                    # Validate that the data is JSON serializable
                    json.dumps(pred_dict)  # Test serialization
                    data.append(pred_dict)
                except Exception as e:
                    print(f"Warning: Skipping corrupted prediction record: {e}")
                    continue

            # Use atomic write: write to temp file first, then rename
            temp_file = None
            try:
                # Create temporary file in the same directory as target file
                temp_fd, temp_path = tempfile.mkstemp(
                    suffix='.tmp',
                    prefix='prediction_history_',
                    dir=os.path.dirname(os.path.abspath(self.data_file)) or '.'
                )

                with os.fdopen(temp_fd, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                    f.flush()  # Ensure data is written to disk
                    os.fsync(f.fileno())  # Force write to disk

                # Atomic rename (on most systems)
                if os.path.exists(self.data_file):
                    # Create backup of existing file
                    backup_path = f"{self.data_file}.backup"
                    shutil.copy2(self.data_file, backup_path)

                # Replace the original file
                shutil.move(temp_path, self.data_file)
                temp_file = None  # Successfully moved, don't clean up

            finally:
                # Clean up temp file if something went wrong
                if temp_file and os.path.exists(temp_path):
                    try:
                        os.unlink(temp_path)
                    except:
                        pass

        except Exception as e:
            print(f"Error saving prediction history: {e}")
            # Try to recover from backup if available
            self._attempt_recovery()
    
    def add_prediction(self, score: Tuple[int, int], player1_name: str, player2_name: str,
                      player1_code: str, player2_code: str, predicted_winner: str,
                      prediction_probability: float, confidence: float,
                      momentum_factors: Dict = None, set_number: int = 1,
                      match_format: str = "Bo3", favorite: str = None,
                      favorite_odds: float = None, surface: str = None,
                      previous_sets_winner: List[str] = None, is_ai_prediction: bool = False,
                      ai_analysis_text: str = None, ai_model_used: str = None,
                      prompt_weights: Dict[str, float] = None, context_factors: Dict[str, Any] = None,
                      learning_metadata: Dict[str, Any] = None, prompt_version: str = None,
                      weight_source: str = None, session_id: str = None,
                      match_status: str = "pending", prediction_id: str = None) -> PredictionRecord:
        """Add a new prediction"""
        record = PredictionRecord(
            timestamp=datetime.now().isoformat(),
            score=score,
            player1_name=player1_name,
            player2_name=player2_name,
            player1_code=player1_code,
            player2_code=player2_code,
            predicted_winner=predicted_winner,
            prediction_probability=prediction_probability,
            confidence=confidence,
            momentum_factors=momentum_factors,
            set_number=set_number,
            match_format=match_format,
            favorite=favorite,
            favorite_odds=favorite_odds,
            surface=surface,
            previous_sets_winner=previous_sets_winner,
            is_ai_prediction=is_ai_prediction,
            ai_analysis_text=ai_analysis_text,
            ai_model_used=ai_model_used,
            prompt_weights=prompt_weights,
            context_factors=context_factors,
            learning_metadata=learning_metadata,
            prompt_version=prompt_version,
            weight_source=weight_source,
            session_id=session_id,
            match_status=match_status,
            prediction_id=prediction_id
        )
        self.predictions.append(record)
        self.save_data()
        return record
    
    def update_prediction_outcome(self, record: PredictionRecord, actual_winner: str):
        """Update a prediction with the actual outcome"""
        record.actual_winner = actual_winner

        # Auto-mark AI predictions as completed when outcome is recorded
        if record.is_ai_prediction and record.match_status in ['pending', 'draft', None]:
            record.match_status = 'completed'
            print(f"🔄 Auto-marked AI prediction as completed: {record.predicted_winner} vs {actual_winner}")

        self.save_data()

    def is_prediction_eligible_for_learning(self, record: PredictionRecord) -> bool:
        """Check if a prediction is eligible for learning (completed match with outcome)"""
        if not record.actual_winner:
            return False

        # Only allow learning from completed matches
        if record.match_status in ["pending", "draft"]:
            return False

        # Must be from a completed match
        if record.match_status != "completed":
            return False

        return True

    def update_match_status(self, session_id: str, new_status: str):
        """Update the match status for all predictions from a specific session"""
        updated_count = 0
        for prediction in self.predictions:
            if prediction.session_id == session_id:
                prediction.match_status = new_status
                updated_count += 1

        if updated_count > 0:
            self.save_data()
            print(f"Updated {updated_count} predictions to status '{new_status}' for session {session_id}")

        return updated_count
    
    def get_latest_prediction(self) -> Optional[PredictionRecord]:
        """Get the most recent prediction without an outcome"""
        for pred in reversed(self.predictions):
            if pred.actual_winner is None:
                return pred
        return None
    
    def get_statistics_by_score(self) -> Dict[str, Dict]:
        """Get accuracy statistics grouped by score scenario"""
        stats = defaultdict(lambda: {
            'total': 0,
            'correct': 0,
            'incorrect': 0,
            'pending': 0,
            'accuracy': 0.0,
            'avg_confidence': 0.0,
            'avg_probability': 0.0,
            'confidence_when_correct': 0.0,
            'confidence_when_wrong': 0.0
        })
        
        # Group predictions by score
        for pred in self.predictions:
            score_key = f"{pred.score[0]}-{pred.score[1]}"
            stats[score_key]['total'] += 1
            
            if pred.actual_winner is None:
                stats[score_key]['pending'] += 1
            elif pred.predicted_winner == pred.actual_winner:
                stats[score_key]['correct'] += 1
            else:
                stats[score_key]['incorrect'] += 1
        
        # Calculate detailed statistics
        for score_key, score_stats in stats.items():
            completed = score_stats['correct'] + score_stats['incorrect']
            
            if completed > 0:
                score_stats['accuracy'] = score_stats['correct'] / completed * 100
            
            # Calculate average confidence and probability
            score_predictions = [p for p in self.predictions 
                               if f"{p.score[0]}-{p.score[1]}" == score_key]
            
            if score_predictions:
                score_stats['avg_confidence'] = sum(p.confidence for p in score_predictions) / len(score_predictions) * 100
                score_stats['avg_probability'] = sum(p.prediction_probability for p in score_predictions) / len(score_predictions) * 100
            
            # Calculate confidence when correct vs wrong
            correct_preds = [p for p in score_predictions 
                           if p.actual_winner == p.predicted_winner and p.actual_winner is not None]
            wrong_preds = [p for p in score_predictions 
                         if p.actual_winner != p.predicted_winner and p.actual_winner is not None]
            
            if correct_preds:
                score_stats['confidence_when_correct'] = sum(p.confidence for p in correct_preds) / len(correct_preds) * 100
            
            if wrong_preds:
                score_stats['confidence_when_wrong'] = sum(p.confidence for p in wrong_preds) / len(wrong_preds) * 100
        
        return dict(stats)
    
    def get_overall_statistics(self) -> Dict:
        """Get overall prediction statistics"""
        total = len(self.predictions)
        if total == 0:
            return {
                'total_predictions': 0,
                'completed': 0,
                'pending': 0,
                'correct': 0,
                'accuracy': 0.0,
                'tied_score_total': 0,
                'tied_score_completed': 0,
                'tied_score_correct': 0,
                'tied_score_accuracy': 0.0
            }
        
        completed = sum(1 for p in self.predictions if p.actual_winner is not None)
        correct = sum(1 for p in self.predictions 
                     if p.actual_winner is not None and p.predicted_winner == p.actual_winner)
        pending = total - completed
        
        # Tied score predictions (3-3, 4-4, 5-5, 6-6)
        tied_predictions = [p for p in self.predictions if p.score[0] == p.score[1]]
        tied_completed = sum(1 for p in tied_predictions if p.actual_winner is not None)
        tied_correct = sum(1 for p in tied_predictions 
                          if p.actual_winner is not None and p.predicted_winner == p.actual_winner)
        
        return {
            'total_predictions': total,
            'completed': completed,
            'pending': pending,
            'correct': correct,
            'accuracy': (correct / completed * 100) if completed > 0 else 0.0,
            'tied_score_total': len(tied_predictions),
            'tied_score_completed': tied_completed,
            'tied_score_correct': tied_correct,
            'tied_score_accuracy': (tied_correct / tied_completed * 100) if tied_completed > 0 else 0.0
        }
    
    def get_statistics_by_set(self) -> Dict[int, Dict]:
        """Get accuracy statistics grouped by set number"""
        set_stats = defaultdict(lambda: {
            'total': 0,
            'correct': 0,
            'incorrect': 0,
            'pending': 0,
            'accuracy': 0.0,
            'tied_score_total': 0,
            'tied_score_correct': 0,
            'tied_score_accuracy': 0.0,
            'avg_confidence': 0.0,
            'confidence_when_correct': 0.0,
            'confidence_when_wrong': 0.0,
            'score_breakdown': defaultdict(lambda: {'total': 0, 'correct': 0, 'accuracy': 0.0})
        })
        
        # Group predictions by set number
        for pred in self.predictions:
            set_num = pred.set_number
            score_key = f"{pred.score[0]}-{pred.score[1]}"
            
            set_stats[set_num]['total'] += 1
            set_stats[set_num]['score_breakdown'][score_key]['total'] += 1
            
            if pred.actual_winner is None:
                set_stats[set_num]['pending'] += 1
            elif pred.predicted_winner == pred.actual_winner:
                set_stats[set_num]['correct'] += 1
                set_stats[set_num]['score_breakdown'][score_key]['correct'] += 1
            else:
                set_stats[set_num]['incorrect'] += 1
            
            # Track tied scores separately
            if pred.score[0] == pred.score[1]:
                set_stats[set_num]['tied_score_total'] += 1
                if pred.actual_winner is not None:
                    if pred.predicted_winner == pred.actual_winner:
                        set_stats[set_num]['tied_score_correct'] += 1
        
        # Calculate detailed statistics for each set
        for set_num, stats in set_stats.items():
            completed = stats['correct'] + stats['incorrect']
            
            if completed > 0:
                stats['accuracy'] = stats['correct'] / completed * 100
            
            # Calculate tied score accuracy
            tied_completed = stats['tied_score_total'] - sum(1 for p in self.predictions 
                                                            if p.set_number == set_num 
                                                            and p.score[0] == p.score[1] 
                                                            and p.actual_winner is None)
            if tied_completed > 0:
                stats['tied_score_accuracy'] = stats['tied_score_correct'] / tied_completed * 100
            
            # Calculate average confidence
            set_predictions = [p for p in self.predictions if p.set_number == set_num]
            if set_predictions:
                stats['avg_confidence'] = sum(p.confidence for p in set_predictions) / len(set_predictions) * 100
            
            # Calculate confidence when correct vs wrong
            correct_preds = [p for p in set_predictions 
                           if p.actual_winner == p.predicted_winner and p.actual_winner is not None]
            wrong_preds = [p for p in set_predictions 
                         if p.actual_winner != p.predicted_winner and p.actual_winner is not None]
            
            if correct_preds:
                stats['confidence_when_correct'] = sum(p.confidence for p in correct_preds) / len(correct_preds) * 100
            
            if wrong_preds:
                stats['confidence_when_wrong'] = sum(p.confidence for p in wrong_preds) / len(wrong_preds) * 100
            
            # Calculate accuracy for each score
            for score_key, score_data in stats['score_breakdown'].items():
                completed_for_score = score_data['correct'] + (score_data['total'] - score_data['correct'] - 
                                                              sum(1 for p in self.predictions 
                                                                  if p.set_number == set_num 
                                                                  and f"{p.score[0]}-{p.score[1]}" == score_key 
                                                                  and p.actual_winner is None))
                if completed_for_score > 0:
                    score_data['accuracy'] = score_data['correct'] / completed_for_score * 100
        
        # Convert defaultdict to regular dict for serialization
        for set_num, stats in set_stats.items():
            stats['score_breakdown'] = dict(stats['score_breakdown'])
        
        return dict(set_stats)

    def get_ai_vs_mathematical_statistics(self) -> Dict:
        """Get statistics comparing AI predictions vs Mathematical predictions"""
        ai_predictions = [p for p in self.predictions if p.is_ai_prediction]
        math_predictions = [p for p in self.predictions if not p.is_ai_prediction]

        def calculate_stats(predictions):
            if not predictions:
                return {
                    'total': 0,
                    'completed': 0,
                    'pending': 0,
                    'correct': 0,
                    'accuracy': 0.0
                }

            total = len(predictions)
            completed = sum(1 for p in predictions if p.actual_winner is not None)
            correct = sum(1 for p in predictions
                         if p.actual_winner is not None and p.predicted_winner == p.actual_winner)
            pending = total - completed
            accuracy = (correct / completed * 100) if completed > 0 else 0.0

            return {
                'total': total,
                'completed': completed,
                'pending': pending,
                'correct': correct,
                'accuracy': accuracy
            }

        return {
            'ai_predictions': calculate_stats(ai_predictions),
            'mathematical_predictions': calculate_stats(math_predictions),
            'total_predictions': len(self.predictions)
        }

    def get_ai_vs_math_by_score(self) -> Dict[str, Dict]:
        """Get AI vs Mathematical statistics broken down by score"""
        stats = defaultdict(lambda: {
            'ai': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            'math': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            'total': 0
        })

        for pred in self.predictions:
            if pred.actual_winner is None:
                continue  # Skip pending predictions

            score_key = f"{pred.score[0]}-{pred.score[1]}"
            pred_type = 'ai' if pred.is_ai_prediction else 'math'

            stats[score_key][pred_type]['total'] += 1
            stats[score_key]['total'] += 1

            if pred.predicted_winner == pred.actual_winner:
                stats[score_key][pred_type]['correct'] += 1

        # Calculate accuracies
        for score_key, score_stats in stats.items():
            for pred_type in ['ai', 'math']:
                if score_stats[pred_type]['total'] > 0:
                    score_stats[pred_type]['accuracy'] = (
                        score_stats[pred_type]['correct'] / score_stats[pred_type]['total'] * 100
                    )

        return dict(stats)

    def get_ai_vs_math_by_set(self) -> Dict[int, Dict]:
        """Get AI vs Mathematical statistics broken down by set number"""
        stats = defaultdict(lambda: {
            'ai': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            'math': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            'total': 0
        })

        for pred in self.predictions:
            if pred.actual_winner is None:
                continue  # Skip pending predictions

            set_num = pred.set_number
            pred_type = 'ai' if pred.is_ai_prediction else 'math'

            stats[set_num][pred_type]['total'] += 1
            stats[set_num]['total'] += 1

            if pred.predicted_winner == pred.actual_winner:
                stats[set_num][pred_type]['correct'] += 1

        # Calculate accuracies
        for set_num, set_stats in stats.items():
            for pred_type in ['ai', 'math']:
                if set_stats[pred_type]['total'] > 0:
                    set_stats[pred_type]['accuracy'] = (
                        set_stats[pred_type]['correct'] / set_stats[pred_type]['total'] * 100
                    )

        return dict(stats)

    def get_statistics_by_tournament_level(self) -> Dict[str, Dict]:
        """Get accuracy statistics grouped by tournament level (ATP, Challenger, WTA, Mixed)"""
        tournament_stats = defaultdict(lambda: {
            'total': 0,
            'correct': 0,
            'incorrect': 0,
            'pending': 0,
            'accuracy': 0.0,
            'ai_predictions': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            'math_predictions': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            'avg_confidence': 0.0,
            'confidence_when_correct': 0.0,
            'surface_breakdown': defaultdict(lambda: {'total': 0, 'correct': 0, 'accuracy': 0.0}),
            'set_breakdown': defaultdict(lambda: {'total': 0, 'correct': 0, 'accuracy': 0.0})
        })

        for pred in self.predictions:
            # Get tournament level from learning metadata or context factors
            tournament_level = self._get_tournament_level(pred)

            stats = tournament_stats[tournament_level]
            stats['total'] += 1

            if pred.actual_winner is None:
                stats['pending'] += 1
            else:
                is_correct = (pred.predicted_winner == pred.actual_winner)

                if is_correct:
                    stats['correct'] += 1
                else:
                    stats['incorrect'] += 1

                # AI vs Math breakdown
                if pred.is_ai_prediction:
                    stats['ai_predictions']['total'] += 1
                    if is_correct:
                        stats['ai_predictions']['correct'] += 1
                else:
                    stats['math_predictions']['total'] += 1
                    if is_correct:
                        stats['math_predictions']['correct'] += 1

                # Surface breakdown
                surface = pred.surface or 'Unknown'
                stats['surface_breakdown'][surface]['total'] += 1
                if is_correct:
                    stats['surface_breakdown'][surface]['correct'] += 1

                # Set breakdown
                set_num = pred.set_number
                stats['set_breakdown'][set_num]['total'] += 1
                if is_correct:
                    stats['set_breakdown'][set_num]['correct'] += 1

        # Calculate accuracies and confidence metrics
        for tournament_level, stats in tournament_stats.items():
            completed = stats['correct'] + stats['incorrect']
            if completed > 0:
                stats['accuracy'] = (stats['correct'] / completed) * 100

                # Calculate average confidence for completed predictions
                completed_preds = [p for p in self.predictions
                                 if p.actual_winner is not None and
                                 self._get_tournament_level(p) == tournament_level]

                if completed_preds:
                    stats['avg_confidence'] = sum(p.confidence for p in completed_preds) / len(completed_preds) * 100

                    correct_preds = [p for p in completed_preds if p.predicted_winner == p.actual_winner]
                    if correct_preds:
                        stats['confidence_when_correct'] = sum(p.confidence for p in correct_preds) / len(correct_preds) * 100

            # Calculate AI vs Math accuracies
            if stats['ai_predictions']['total'] > 0:
                stats['ai_predictions']['accuracy'] = (
                    stats['ai_predictions']['correct'] / stats['ai_predictions']['total'] * 100
                )
            if stats['math_predictions']['total'] > 0:
                stats['math_predictions']['accuracy'] = (
                    stats['math_predictions']['correct'] / stats['math_predictions']['total'] * 100
                )

            # Calculate surface breakdown accuracies
            for surface, surface_stats in stats['surface_breakdown'].items():
                if surface_stats['total'] > 0:
                    surface_stats['accuracy'] = (surface_stats['correct'] / surface_stats['total']) * 100

            # Calculate set breakdown accuracies
            for set_num, set_stats in stats['set_breakdown'].items():
                if set_stats['total'] > 0:
                    set_stats['accuracy'] = (set_stats['correct'] / set_stats['total']) * 100

        # Convert defaultdicts to regular dicts for JSON serialization
        for tournament_level, stats in tournament_stats.items():
            stats['surface_breakdown'] = dict(stats['surface_breakdown'])
            stats['set_breakdown'] = dict(stats['set_breakdown'])

        return dict(tournament_stats)

    def _get_tournament_level(self, pred: 'PredictionRecord') -> str:
        """Helper method to extract tournament level from prediction record"""
        if pred.learning_metadata and 'tournament_level' in pred.learning_metadata:
            return pred.learning_metadata['tournament_level']
        elif pred.context_factors and 'tournament_level' in pred.context_factors:
            return pred.context_factors['tournament_level']
        return 'Mixed'

    def get_prediction_patterns(self) -> Dict[str, any]:
        """Get patterns and insights about prediction behavior"""
        if not self.predictions:
            return {}

        completed_predictions = [p for p in self.predictions if p.actual_winner is not None]
        ai_predictions = [p for p in completed_predictions if p.is_ai_prediction]
        math_predictions = [p for p in completed_predictions if not p.is_ai_prediction]

        patterns = {}

        # Most predicted scores
        ai_scores = defaultdict(int)
        math_scores = defaultdict(int)

        for pred in ai_predictions:
            score_key = f"{pred.score[0]}-{pred.score[1]}"
            ai_scores[score_key] += 1

        for pred in math_predictions:
            score_key = f"{pred.score[0]}-{pred.score[1]}"
            math_scores[score_key] += 1

        patterns['ai_most_predicted_scores'] = sorted(ai_scores.items(), key=lambda x: x[1], reverse=True)[:5]
        patterns['math_most_predicted_scores'] = sorted(math_scores.items(), key=lambda x: x[1], reverse=True)[:5]

        # Most predicted sets
        ai_sets = defaultdict(int)
        math_sets = defaultdict(int)

        for pred in ai_predictions:
            ai_sets[pred.set_number] += 1

        for pred in math_predictions:
            math_sets[pred.set_number] += 1

        patterns['ai_most_predicted_sets'] = sorted(ai_sets.items(), key=lambda x: x[1], reverse=True)
        patterns['math_most_predicted_sets'] = sorted(math_sets.items(), key=lambda x: x[1], reverse=True)

        # Accuracy by confidence level
        def get_confidence_accuracy(predictions):
            high_conf = [p for p in predictions if p.confidence >= 0.8]
            med_conf = [p for p in predictions if 0.5 <= p.confidence < 0.8]
            low_conf = [p for p in predictions if p.confidence < 0.5]

            def calc_acc(preds):
                if not preds:
                    return 0.0
                correct = sum(1 for p in preds if p.predicted_winner == p.actual_winner)
                return correct / len(preds) * 100

            return {
                'high_confidence': {'count': len(high_conf), 'accuracy': calc_acc(high_conf)},
                'medium_confidence': {'count': len(med_conf), 'accuracy': calc_acc(med_conf)},
                'low_confidence': {'count': len(low_conf), 'accuracy': calc_acc(low_conf)}
            }

        patterns['ai_confidence_accuracy'] = get_confidence_accuracy(ai_predictions)
        patterns['math_confidence_accuracy'] = get_confidence_accuracy(math_predictions)

        # Tied vs non-tied score performance
        ai_tied = [p for p in ai_predictions if p.score[0] == p.score[1]]
        ai_non_tied = [p for p in ai_predictions if p.score[0] != p.score[1]]
        math_tied = [p for p in math_predictions if p.score[0] == p.score[1]]
        math_non_tied = [p for p in math_predictions if p.score[0] != p.score[1]]

        def calc_acc(preds):
            if not preds:
                return 0.0
            correct = sum(1 for p in preds if p.predicted_winner == p.actual_winner)
            return correct / len(preds) * 100

        patterns['score_type_performance'] = {
            'ai_tied_scores': {'count': len(ai_tied), 'accuracy': calc_acc(ai_tied)},
            'ai_non_tied_scores': {'count': len(ai_non_tied), 'accuracy': calc_acc(ai_non_tied)},
            'math_tied_scores': {'count': len(math_tied), 'accuracy': calc_acc(math_tied)},
            'math_non_tied_scores': {'count': len(math_non_tied), 'accuracy': calc_acc(math_non_tied)}
        }

        return patterns

    def get_recent_predictions(self, limit: int = 10) -> List[Tuple[int, PredictionRecord]]:
        """Get the most recent predictions with their indices"""
        start_idx = max(0, len(self.predictions) - limit)
        result = []
        for i in range(len(self.predictions) - 1, start_idx - 1, -1):
            if i >= 0:
                result.append((i, self.predictions[i]))
        return result
    
    def export_statistics(self, filename: str = "prediction_statistics.txt"):
        """Export detailed statistics to a file with enhanced AI vs Mathematical comparison"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("Tennis Set Prediction Statistics\n")
            f.write("=" * 80 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Overall statistics
            overall = self.get_overall_statistics()
            ai_vs_math = self.get_ai_vs_mathematical_statistics()

            f.write("OVERALL PERFORMANCE SUMMARY\n")
            f.write("=" * 80 + "\n")
            f.write(f"Total Predictions: {overall['total_predictions']}\n")
            f.write(f"Completed: {overall['completed']}\n")
            f.write(f"Pending: {overall['pending']}\n")
            f.write(f"Overall Accuracy: {overall['accuracy']:.1f}%\n")
            f.write(f"Tied Score Accuracy: {overall['tied_score_accuracy']:.1f}%\n\n")

            # AI vs Mathematical Summary
            f.write("AI vs MATHEMATICAL PREDICTIONS SUMMARY\n")
            f.write("-" * 80 + "\n")
            ai_stats = ai_vs_math['ai_predictions']
            math_stats = ai_vs_math['mathematical_predictions']

            f.write(f"🤖 AI Predictions:\n")
            f.write(f"   Total: {ai_stats['total']} | Completed: {ai_stats['completed']} | Accuracy: {ai_stats['accuracy']:.1f}%\n\n")
            f.write(f"📊 Mathematical Predictions:\n")
            f.write(f"   Total: {math_stats['total']} | Completed: {math_stats['completed']} | Accuracy: {math_stats['accuracy']:.1f}%\n\n")

            # Performance comparison
            if ai_stats['total'] > 0 and math_stats['total'] > 0:
                if ai_stats['accuracy'] > math_stats['accuracy']:
                    leader = "AI"
                    diff = ai_stats['accuracy'] - math_stats['accuracy']
                elif math_stats['accuracy'] > ai_stats['accuracy']:
                    leader = "Mathematical"
                    diff = math_stats['accuracy'] - ai_stats['accuracy']
                else:
                    leader = "Tied"
                    diff = 0

                f.write(f"🏆 Performance Leader: {leader}")
                if diff > 0:
                    f.write(f" (Advantage: +{diff:.1f}%)")
                f.write("\n\n")

            # AI vs Mathematical Comparison by Score
            f.write("AI vs MATHEMATICAL PERFORMANCE BY SCORE\n")
            f.write("=" * 80 + "\n")
            score_comparison = self.get_ai_vs_math_by_score()

            # Table header
            f.write(f"{'Score':<8} {'AI Total':<10} {'AI Acc%':<10} {'Math Total':<12} {'Math Acc%':<12} {'Winner':<10} {'Diff':<8}\n")
            f.write("-" * 80 + "\n")

            # Sort scores for display (tied scores first)
            tied_scores = ['3-3', '4-4', '5-5', '6-6']
            other_scores = sorted([s for s in score_comparison.keys() if s not in tied_scores])
            all_scores = [s for s in tied_scores if s in score_comparison] + other_scores

            for score in all_scores:
                stats = score_comparison[score]
                ai_acc = stats['ai']['accuracy']
                math_acc = stats['math']['accuracy']

                # Determine winner
                if stats['ai']['total'] == 0 and stats['math']['total'] == 0:
                    continue
                elif stats['ai']['total'] == 0:
                    winner = "Math"
                    diff = f"+{math_acc:.1f}%"
                elif stats['math']['total'] == 0:
                    winner = "AI"
                    diff = f"+{ai_acc:.1f}%"
                elif ai_acc > math_acc:
                    winner = "AI"
                    diff = f"+{ai_acc - math_acc:.1f}%"
                elif math_acc > ai_acc:
                    winner = "Math"
                    diff = f"+{math_acc - ai_acc:.1f}%"
                else:
                    winner = "Tie"
                    diff = "0.0%"

                # Mark tied scores with asterisk
                score_display = f"{score}*" if score in tied_scores else score

                f.write(f"{score_display:<8} {stats['ai']['total']:<10} {ai_acc:<10.1f} {stats['math']['total']:<12} {math_acc:<12.1f} {winner:<10} {diff:<8}\n")

            f.write("\n* = Tied scores (pressure situations)\n\n")

            # AI vs Mathematical Comparison by Set
            f.write("AI vs MATHEMATICAL PERFORMANCE BY SET\n")
            f.write("=" * 80 + "\n")
            set_comparison = self.get_ai_vs_math_by_set()

            # Table header
            f.write(f"{'Set':<6} {'AI Total':<10} {'AI Acc%':<10} {'Math Total':<12} {'Math Acc%':<12} {'Winner':<10} {'Diff':<8}\n")
            f.write("-" * 80 + "\n")

            for set_num in sorted(set_comparison.keys()):
                stats = set_comparison[set_num]
                ai_acc = stats['ai']['accuracy']
                math_acc = stats['math']['accuracy']

                # Determine winner
                if stats['ai']['total'] == 0 and stats['math']['total'] == 0:
                    continue
                elif stats['ai']['total'] == 0:
                    winner = "Math"
                    diff = f"+{math_acc:.1f}%"
                elif stats['math']['total'] == 0:
                    winner = "AI"
                    diff = f"+{ai_acc:.1f}%"
                elif ai_acc > math_acc:
                    winner = "AI"
                    diff = f"+{ai_acc - math_acc:.1f}%"
                elif math_acc > ai_acc:
                    winner = "Math"
                    diff = f"+{math_acc - ai_acc:.1f}%"
                else:
                    winner = "Tie"
                    diff = "0.0%"

                f.write(f"{set_num:<6} {stats['ai']['total']:<10} {ai_acc:<10.1f} {stats['math']['total']:<12} {math_acc:<12.1f} {winner:<10} {diff:<8}\n")

            f.write("\n")

            # Tournament Level Statistics
            f.write("TOURNAMENT LEVEL STATISTICS\n")
            f.write("=" * 80 + "\n")
            tournament_stats = self.get_statistics_by_tournament_level()

            if tournament_stats:
                f.write(f"{'Tournament':<12} {'Total':<8} {'Correct':<8} {'Accuracy':<10} {'AI Acc%':<10} {'Math Acc%':<12} {'Advantage':<12}\n")
                f.write("-" * 80 + "\n")

                # Sort by total predictions (descending)
                sorted_tournaments = sorted(tournament_stats.items(),
                                          key=lambda x: x[1]['total'], reverse=True)

                for tournament_level, stats in sorted_tournaments:
                    ai_acc = stats['ai_predictions']['accuracy'] if stats['ai_predictions']['total'] > 0 else 0
                    math_acc = stats['math_predictions']['accuracy'] if stats['math_predictions']['total'] > 0 else 0

                    # Determine advantage
                    if ai_acc > 0 and math_acc > 0:
                        if ai_acc > math_acc:
                            advantage = f"AI +{ai_acc - math_acc:.1f}%"
                        elif math_acc > ai_acc:
                            advantage = f"Math +{math_acc - ai_acc:.1f}%"
                        else:
                            advantage = "Tied"
                    else:
                        advantage = "N/A"

                    f.write(f"{tournament_level:<12} {stats['total']:<8} {stats['correct']:<8} "
                           f"{stats['accuracy']:<10.1f} {ai_acc:<10.1f} {math_acc:<12.1f} {advantage:<12}\n")

                # Surface breakdown by tournament level
                f.write(f"\nSurface Breakdown by Tournament Level:\n")
                f.write("-" * 60 + "\n")

                for tournament_level, stats in sorted_tournaments:
                    if stats['surface_breakdown']:
                        f.write(f"\n{tournament_level}:\n")
                        for surface, surface_stats in stats['surface_breakdown'].items():
                            if surface_stats['total'] > 0:
                                f.write(f"  {surface}: {surface_stats['accuracy']:.1f}% ({surface_stats['total']} predictions)\n")
            else:
                f.write("No tournament level data available.\n")

            f.write("\n")

            # Prediction Patterns and Insights
            f.write("PREDICTION PATTERNS & INSIGHTS\n")
            f.write("=" * 80 + "\n")
            patterns = self.get_prediction_patterns()

            if patterns:
                # Most predicted scores
                f.write("Most Predicted Scores:\n")
                f.write("-" * 40 + "\n")
                ai_scores = patterns.get('ai_most_predicted_scores', [])
                math_scores = patterns.get('math_most_predicted_scores', [])

                f.write("AI Predictions:\n")
                for score, count in ai_scores[:5]:
                    f.write(f"  {score}: {count} predictions\n")

                f.write("\nMathematical Predictions:\n")
                for score, count in math_scores[:5]:
                    f.write(f"  {score}: {count} predictions\n")

                # Most predicted sets
                f.write(f"\nMost Predicted Sets:\n")
                f.write("-" * 40 + "\n")
                ai_sets = patterns.get('ai_most_predicted_sets', [])
                math_sets = patterns.get('math_most_predicted_sets', [])

                f.write("AI Predictions:\n")
                for set_num, count in ai_sets:
                    f.write(f"  Set {set_num}: {count} predictions\n")

                f.write("\nMathematical Predictions:\n")
                for set_num, count in math_sets:
                    f.write(f"  Set {set_num}: {count} predictions\n")

                # Confidence vs Accuracy Analysis
                f.write(f"\nConfidence vs Accuracy Analysis:\n")
                f.write("-" * 40 + "\n")

                ai_conf = patterns.get('ai_confidence_accuracy', {})
                math_conf = patterns.get('math_confidence_accuracy', {})

                f.write("AI Predictions:\n")
                for level_key, level_name in [('high_confidence', 'High'), ('medium_confidence', 'Medium'), ('low_confidence', 'Low')]:
                    if level_key in ai_conf:
                        data = ai_conf[level_key]
                        f.write(f"  {level_name} Confidence: {data['accuracy']:.1f}% ({data['count']} predictions)\n")

                f.write("\nMathematical Predictions:\n")
                for level_key, level_name in [('high_confidence', 'High'), ('medium_confidence', 'Medium'), ('low_confidence', 'Low')]:
                    if level_key in math_conf:
                        data = math_conf[level_key]
                        f.write(f"  {level_name} Confidence: {data['accuracy']:.1f}% ({data['count']} predictions)\n")

                # Tied vs Non-Tied Performance
                f.write(f"\nTied vs Non-Tied Score Performance:\n")
                f.write("-" * 40 + "\n")
                score_perf = patterns.get('score_type_performance', {})
                ai_tied = score_perf.get('ai_tied_scores', {'accuracy': 0})
                ai_non_tied = score_perf.get('ai_non_tied_scores', {'accuracy': 0})
                math_tied = score_perf.get('math_tied_scores', {'accuracy': 0})
                math_non_tied = score_perf.get('math_non_tied_scores', {'accuracy': 0})

                f.write(f"AI - Tied Scores: {ai_tied['accuracy']:.1f}% ({ai_tied.get('count', 0)} predictions)\n")
                f.write(f"AI - Non-Tied Scores: {ai_non_tied['accuracy']:.1f}% ({ai_non_tied.get('count', 0)} predictions)\n")
                f.write(f"Math - Tied Scores: {math_tied['accuracy']:.1f}% ({math_tied.get('count', 0)} predictions)\n")
                f.write(f"Math - Non-Tied Scores: {math_non_tied['accuracy']:.1f}% ({math_non_tied.get('count', 0)} predictions)\n")

            f.write("\n")

            # Detailed Statistics by Score
            f.write("DETAILED STATISTICS BY SCORE\n")
            f.write("=" * 80 + "\n")
            score_stats = self.get_statistics_by_score()

            # Sort scores for better presentation
            tied_scores = ['3-3', '4-4', '5-5', '6-6']
            other_scores = sorted([s for s in score_stats.keys() if s not in tied_scores])

            for score_list, title in [(tied_scores, "Tied Scores"), (other_scores, "Other Scores")]:
                if any(score in score_stats for score in score_list):
                    f.write(f"\n{title}:\n")
                    f.write("-" * 40 + "\n")
                    for score in score_list:
                        if score in score_stats:
                            stats = score_stats[score]
                            f.write(f"\n{score}:\n")
                            f.write(f"  Total: {stats['total']}\n")
                            f.write(f"  Correct: {stats['correct']}\n")
                            f.write(f"  Incorrect: {stats['incorrect']}\n")
                            f.write(f"  Pending: {stats['pending']}\n")
                            f.write(f"  Accuracy: {stats['accuracy']:.1f}%\n")
                            f.write(f"  Avg Confidence: {stats['avg_confidence']:.1f}%\n")
                            f.write(f"  Avg Probability: {stats['avg_probability']:.1f}%\n")
                            if stats['confidence_when_correct'] > 0:
                                f.write(f"  Confidence when Correct: {stats['confidence_when_correct']:.1f}%\n")
                            if stats['confidence_when_wrong'] > 0:
                                f.write(f"  Confidence when Wrong: {stats['confidence_when_wrong']:.1f}%\n")

            # Detailed Statistics by Set Number
            f.write("\n\nDETAILED STATISTICS BY SET NUMBER\n")
            f.write("=" * 80 + "\n")

            set_stats = self.get_statistics_by_set()
            for set_num in sorted(set_stats.keys()):
                stats = set_stats[set_num]
                f.write(f"\nSet {set_num}:\n")
                f.write("-" * 20 + "\n")
                f.write(f"  Total Predictions: {stats['total']}\n")
                f.write(f"  Correct: {stats['correct']}\n")
                f.write(f"  Incorrect: {stats['incorrect']}\n")
                f.write(f"  Pending: {stats['pending']}\n")
                if stats['correct'] + stats['incorrect'] > 0:
                    f.write(f"  Accuracy: {stats['accuracy']:.1f}%\n")
                    f.write(f"  Tied Score Accuracy: {stats['tied_score_accuracy']:.1f}%\n")
                    f.write(f"  Avg Confidence: {stats['avg_confidence']:.1f}%\n")
                    if stats['confidence_when_correct'] > 0:
                        f.write(f"  Confidence when Correct: {stats['confidence_when_correct']:.1f}%\n")
                    if stats['confidence_when_wrong'] > 0:
                        f.write(f"  Confidence when Wrong: {stats['confidence_when_wrong']:.1f}%\n")

                    # Add score breakdown
                    if stats['score_breakdown']:
                        f.write(f"  Score Breakdown:\n")
                        tied_scores = ['3-3', '4-4', '5-5', '6-6']
                        scores = sorted(stats['score_breakdown'].keys(),
                                      key=lambda x: (x not in tied_scores, x))
                        for score in scores:
                            score_data = stats['score_breakdown'][score]
                            f.write(f"    {score}: {score_data['total']} predictions")
                            if score_data['accuracy'] > 0:
                                f.write(f" ({score_data['accuracy']:.1f}% accuracy)")
                            f.write("\n")


            # Key Insights and Recommendations
            f.write("\n\nKEY INSIGHTS & RECOMMENDATIONS\n")
            f.write("=" * 80 + "\n")

            # Generate insights based on the data
            insights = []

            # Overall performance insight
            if ai_stats['total'] > 0 and math_stats['total'] > 0:
                if ai_stats['accuracy'] > math_stats['accuracy'] + 5:
                    insights.append(f"🤖 AI predictions are significantly outperforming mathematical predictions by {ai_stats['accuracy'] - math_stats['accuracy']:.1f}%")
                elif math_stats['accuracy'] > ai_stats['accuracy'] + 5:
                    insights.append(f"📊 Mathematical predictions are significantly outperforming AI predictions by {math_stats['accuracy'] - ai_stats['accuracy']:.1f}%")
                else:
                    insights.append("🤝 AI and Mathematical predictions are performing similarly")

            # Tied score performance
            if overall['tied_score_accuracy'] > overall['accuracy'] + 5:
                insights.append(f"⚡ Performance on tied scores ({overall['tied_score_accuracy']:.1f}%) is better than overall accuracy")
            elif overall['tied_score_accuracy'] < overall['accuracy'] - 5:
                insights.append(f"⚠️ Performance on tied scores ({overall['tied_score_accuracy']:.1f}%) is worse than overall accuracy - focus needed on pressure situations")

            # Sample size recommendations
            if overall['total_predictions'] < 50:
                insights.append("📈 More data needed - aim for at least 50 predictions for reliable statistics")
            elif overall['total_predictions'] < 100:
                insights.append("📊 Good sample size - continue collecting data for more robust insights")
            else:
                insights.append("✅ Excellent sample size - statistics are highly reliable")

            # Confidence analysis
            if patterns:
                ai_conf = patterns.get('ai_confidence_accuracy', {})
                if 'high_confidence' in ai_conf and ai_conf['high_confidence']['count'] > 5:
                    high_acc = ai_conf['high_confidence']['accuracy']
                    if high_acc > 70:
                        insights.append(f"🎯 High confidence AI predictions are very reliable ({high_acc:.1f}% accuracy)")
                    elif high_acc < 50:
                        insights.append(f"⚠️ High confidence AI predictions are underperforming ({high_acc:.1f}% accuracy) - review confidence calibration")

                # Tied vs non-tied performance insights
                score_perf = patterns.get('score_type_performance', {})
                ai_tied = score_perf.get('ai_tied_scores', {})
                ai_non_tied = score_perf.get('ai_non_tied_scores', {})

                if ai_tied.get('count', 0) > 10 and ai_non_tied.get('count', 0) > 10:
                    tied_acc = ai_tied['accuracy']
                    non_tied_acc = ai_non_tied['accuracy']
                    if tied_acc > non_tied_acc + 10:
                        insights.append(f"⚡ AI performs better on tied scores ({tied_acc:.1f}%) than non-tied ({non_tied_acc:.1f}%)")
                    elif non_tied_acc > tied_acc + 10:
                        insights.append(f"⚠️ AI struggles with tied scores ({tied_acc:.1f}%) vs non-tied ({non_tied_acc:.1f}%)")

            # Write insights
            if insights:
                for i, insight in enumerate(insights, 1):
                    f.write(f"{i}. {insight}\n")
            else:
                f.write("No specific insights available yet - continue collecting data.\n")

            f.write(f"\n{'='*80}\n")
            f.write(f"Report generated on {datetime.now().strftime('%Y-%m-%d at %H:%M:%S')}\n")
            f.write(f"Total predictions analyzed: {overall['total_predictions']}\n")
            f.write(f"{'='*80}\n")

        # Also export enhanced betting statistics
        self.export_enhanced_betting_statistics(filename.replace('.txt', '_betting_analysis.txt'))
    
    def clear_history(self):
        """Clear all prediction history"""
        self.predictions = []
        self.save_data()
    
    def clear_pending_predictions(self):
        """Clear only pending predictions (those without outcomes)"""
        # Count pending before clearing
        pending_count = sum(1 for p in self.predictions if p.actual_winner is None)
        # Keep only completed predictions
        self.predictions = [p for p in self.predictions if p.actual_winner is not None]
        self.save_data()
        return pending_count  # Return count of cleared
    
    def delete_prediction(self, index: int) -> bool:
        """Delete a specific prediction by index. Returns True if successful."""
        if 0 <= index < len(self.predictions):
            self.predictions.pop(index)
            self.save_data()
            return True
        return False

    def delete_prediction_by_id(self, prediction_id: str) -> bool:
        """Delete a specific prediction by prediction_id. Returns True if successful."""
        initial_count = len(self.predictions)
        self.predictions = [p for p in self.predictions if p.prediction_id != prediction_id]

        if len(self.predictions) < initial_count:
            self.save_data()
            print(f"   Deleted prediction {prediction_id} from main tracker")
            return True
        return False
    
    def get_prediction_by_index(self, index: int) -> Optional[PredictionRecord]:
        """Get a specific prediction by index"""
        if 0 <= index < len(self.predictions):
            return self.predictions[index]
        return None
    
    def export_enhanced_betting_statistics(self, filename: str = "prediction_statistics_betting_analysis.txt"):
        """Export enhanced statistics optimized for betting decisions"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("Enhanced Tennis Betting Analysis\n")
            f.write("=" * 80 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Predictions Analyzed: {len(self.predictions)}\n\n")
            
            # 1. BEST PERFORMING SCENARIOS
            f.write("BEST PERFORMING SCENARIOS FOR BETTING\n")
            f.write("=" * 80 + "\n\n")
            
            # Score-Set Combinations
            score_set_stats = self._get_score_set_combination_stats()
            f.write("1. Best Score-Set Combinations (min 3 predictions):\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'Score':<10} {'Set':<8} {'Total':<8} {'Accuracy':<12} {'Avg Conf':<12} {'ROI Potential'}\n")
            f.write("-" * 60 + "\n")
            
            # Sort by accuracy
            sorted_combos = sorted(score_set_stats.items(), 
                                 key=lambda x: (x[1]['accuracy'], x[1]['total']), 
                                 reverse=True)
            
            for (score, set_num), stats in sorted_combos:
                if stats['total'] >= 3 and stats['completed'] > 0:
                    accuracy = stats['accuracy']
                    # Calculate ROI potential (simplified)
                    roi = "High" if accuracy >= 70 else "Medium" if accuracy >= 60 else "Low"
                    if accuracy >= 70 and stats['avg_confidence'] > 40:
                        roi = "Very High"
                    
                    f.write(f"{score:<10} Set {set_num:<5} {stats['total']:<8} "
                          f"{accuracy:>6.1f}%     {stats['avg_confidence']:>6.1f}%     {roi}\n")
            
            # 2. CONFIDENCE ANALYSIS
            f.write("\n\n2. Confidence Level Performance:\n")
            f.write("-" * 60 + "\n")
            confidence_bands = self._analyze_confidence_bands()
            f.write(f"{'Confidence Range':<20} {'Predictions':<15} {'Accuracy':<12} {'Recommendation'}\n")
            f.write("-" * 60 + "\n")
            
            for band, stats in sorted(confidence_bands.items()):
                recommendation = self._get_confidence_recommendation(stats['accuracy'])
                f.write(f"{band:<20} {stats['total']:<15} {stats['accuracy']:>6.1f}%     {recommendation}\n")
            
            # 3. MOMENTUM PATTERN SUCCESS
            f.write("\n\n3. Momentum Pattern Success Rates:\n")
            f.write("-" * 60 + "\n")
            momentum_stats = self._analyze_momentum_patterns()
            f.write(f"{'Momentum Type':<25} {'Predictions':<15} {'Accuracy':<12} {'Betting Signal'}\n")
            f.write("-" * 60 + "\n")
            
            for momentum, stats in momentum_stats.items():
                signal = self._get_momentum_signal(momentum, stats['accuracy'])
                f.write(f"{momentum:<25} {stats['total']:<15} {stats['accuracy']:>6.1f}%     {signal}\n")
            
            # 4. FAVORITE VS UNDERDOG ANALYSIS
            f.write("\n\n4. Favorite vs Underdog Performance:\n")
            f.write("-" * 60 + "\n")
            fav_stats = self._analyze_favorite_performance()
            
            for category, stats in fav_stats.items():
                f.write(f"\n{category}:\n")
                f.write(f"  Total Predictions: {stats['total']}\n")
                f.write(f"  Accuracy: {stats['accuracy']:.1f}%\n")
                if stats.get('by_score'):
                    f.write(f"  Best Scores: ")
                    best_scores = sorted(stats['by_score'].items(), 
                                       key=lambda x: x[1]['accuracy'] if x[1]['completed'] > 0 else 0, 
                                       reverse=True)[:3]
                    for score, score_stats in best_scores:
                        if score_stats['total'] >= 2 and score_stats['completed'] > 0:
                            f.write(f"{score} ({score_stats['accuracy']:.0f}%), ")
                    f.write("\n")
            
            # 5. TIME-BASED PERFORMANCE
            f.write("\n\n5. Recent Performance Trends (Last 20 predictions):\n")
            f.write("-" * 60 + "\n")
            recent_stats = self._analyze_recent_performance()
            f.write(f"Recent Accuracy: {recent_stats['recent_accuracy']:.1f}%\n")
            f.write(f"Trend: {recent_stats['trend']}\n")
            if recent_stats.get('best_score'):
                f.write(f"Best Recent Score: {recent_stats['best_score']} "
                       f"({recent_stats['best_score_accuracy']:.0f}%)\n")
            
            # 6. BETTING RECOMMENDATIONS
            f.write("\n\nBETTING RECOMMENDATIONS\n")
            f.write("=" * 80 + "\n\n")
            
            recommendations = self._generate_betting_recommendations()
            for i, rec in enumerate(recommendations, 1):
                f.write(f"{i}. {rec}\n")
            
            # 7. AVOID SCENARIOS
            f.write("\n\nSCENARIOS TO AVOID\n")
            f.write("=" * 80 + "\n\n")
            
            avoid_scenarios = self._identify_avoid_scenarios()
            for i, scenario in enumerate(avoid_scenarios, 1):
                f.write(f"{i}. {scenario}\n")
            
            # 8. STATISTICAL SUMMARY
            f.write("\n\nSTATISTICAL SUMMARY\n")
            f.write("=" * 80 + "\n")
            overall = self.get_overall_statistics()
            f.write(f"\nOverall Accuracy: {overall['accuracy']:.1f}%\n")
            f.write(f"Tied Score Accuracy: {overall['tied_score_accuracy']:.1f}%\n")
            f.write(f"Total Completed Predictions: {overall['completed']}\n")
            f.write(f"Average Confidence: {sum(p.confidence for p in self.predictions) / len(self.predictions) * 100:.1f}%\n")
            
            # Calculate expected value
            high_conf_preds = [p for p in self.predictions 
                             if p.confidence > 0.4 and p.actual_winner is not None]
            if high_conf_preds:
                high_conf_accuracy = sum(1 for p in high_conf_preds 
                                       if p.predicted_winner == p.actual_winner) / len(high_conf_preds)
                f.write(f"\nHigh Confidence (>40%) Predictions: {len(high_conf_preds)}\n")
                f.write(f"High Confidence Accuracy: {high_conf_accuracy * 100:.1f}%\n")
                
                # Simple EV calculation (assuming even odds for simplicity)
                ev = (high_conf_accuracy * 1) - ((1 - high_conf_accuracy) * 1)
                f.write(f"Expected Value (High Conf): {ev:+.3f} units per bet\n")
    
    def _get_score_set_combination_stats(self) -> Dict:
        """Get statistics for each score-set combination"""
        combo_stats = defaultdict(lambda: {
            'total': 0, 'correct': 0, 'completed': 0, 
            'accuracy': 0.0, 'avg_confidence': 0.0
        })
        
        for pred in self.predictions:
            key = (f"{pred.score[0]}-{pred.score[1]}", pred.set_number)
            combo_stats[key]['total'] += 1
            combo_stats[key]['avg_confidence'] += pred.confidence * 100
            
            if pred.actual_winner is not None:
                combo_stats[key]['completed'] += 1
                if pred.predicted_winner == pred.actual_winner:
                    combo_stats[key]['correct'] += 1
        
        # Calculate averages and accuracy
        for key, stats in combo_stats.items():
            if stats['total'] > 0:
                stats['avg_confidence'] /= stats['total']
            if stats['completed'] > 0:
                stats['accuracy'] = (stats['correct'] / stats['completed']) * 100
        
        return dict(combo_stats)
    
    def _analyze_confidence_bands(self) -> Dict:
        """Analyze performance by confidence levels"""
        bands = {
            '20-30%': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            '30-40%': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            '40-50%': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            '50-60%': {'total': 0, 'correct': 0, 'accuracy': 0.0},
            '60%+': {'total': 0, 'correct': 0, 'accuracy': 0.0}
        }
        
        for pred in self.predictions:
            if pred.actual_winner is None:
                continue
                
            conf_pct = pred.confidence * 100
            if conf_pct < 30:
                band = '20-30%'
            elif conf_pct < 40:
                band = '30-40%'
            elif conf_pct < 50:
                band = '40-50%'
            elif conf_pct < 60:
                band = '50-60%'
            else:
                band = '60%+'
            
            bands[band]['total'] += 1
            if pred.predicted_winner == pred.actual_winner:
                bands[band]['correct'] += 1
        
        # Calculate accuracy
        for band, stats in bands.items():
            if stats['total'] > 0:
                stats['accuracy'] = (stats['correct'] / stats['total']) * 100
        
        return bands
    
    def _get_confidence_recommendation(self, accuracy: float) -> str:
        """Get betting recommendation based on confidence band accuracy"""
        if accuracy >= 70:
            return "Strong Bet"
        elif accuracy >= 60:
            return "Consider"
        elif accuracy >= 50:
            return "Marginal"
        else:
            return "Avoid"
    
    def _analyze_momentum_patterns(self) -> Dict:
        """Analyze success rates by momentum patterns"""
        momentum_stats = defaultdict(lambda: {'total': 0, 'correct': 0, 'accuracy': 0.0})
        
        for pred in self.predictions:
            if pred.actual_winner is None or not pred.momentum_factors:
                continue
            
            # Get momentum for predicted winner
            for player, factors in pred.momentum_factors.items():
                if player == pred.predicted_winner:
                    # Handle both dict and ServePattern object
                    if isinstance(factors, dict):
                        momentum = factors.get('current_momentum', 'neutral')
                    elif hasattr(factors, 'current_momentum'):
                        # It's a ServePattern object
                        momentum = factors.current_momentum
                        # Convert enum to string if needed
                        if hasattr(momentum, 'value'):
                            momentum = momentum.value
                    else:
                        momentum = 'neutral'
                    
                    momentum_stats[momentum]['total'] += 1
                    if pred.predicted_winner == pred.actual_winner:
                        momentum_stats[momentum]['correct'] += 1
        
        # Calculate accuracy
        for momentum, stats in momentum_stats.items():
            if stats['total'] > 0:
                stats['accuracy'] = (stats['correct'] / stats['total']) * 100
        
        return dict(momentum_stats)
    
    def _get_momentum_signal(self, momentum: str, accuracy: float) -> str:
        """Get betting signal based on momentum type and accuracy"""
        if momentum == 'strong_serving' and accuracy >= 60:
            return "BET ON"
        elif momentum == 'weak_serving' and accuracy < 40:
            return "FADE (bet against)"
        elif momentum == 'momentum_shift' and accuracy < 35:
            return "STRONG FADE"
        elif accuracy >= 65:
            return "FOLLOW"
        else:
            return "NEUTRAL"
    
    def _analyze_favorite_performance(self) -> Dict:
        """Analyze performance when predicting favorites vs underdogs"""
        stats = {
            'Predicting Favorites': {
                'total': 0, 'correct': 0, 'accuracy': 0.0,
                'by_score': defaultdict(lambda: {'total': 0, 'correct': 0, 'completed': 0, 'accuracy': 0.0})
            },
            'Predicting Underdogs': {
                'total': 0, 'correct': 0, 'accuracy': 0.0,
                'by_score': defaultdict(lambda: {'total': 0, 'correct': 0, 'completed': 0, 'accuracy': 0.0})
            },
            'No Favorite': {
                'total': 0, 'correct': 0, 'accuracy': 0.0
            }
        }
        
        for pred in self.predictions:
            if pred.actual_winner is None:
                continue
            
            score_key = f"{pred.score[0]}-{pred.score[1]}"
            
            if pred.favorite and pred.favorite in [pred.player1_code, pred.player2_code]:
                if pred.predicted_winner == pred.favorite:
                    cat = 'Predicting Favorites'
                else:
                    cat = 'Predicting Underdogs'
                
                stats[cat]['total'] += 1
                stats[cat]['by_score'][score_key]['total'] += 1
                stats[cat]['by_score'][score_key]['completed'] += 1
                
                if pred.predicted_winner == pred.actual_winner:
                    stats[cat]['correct'] += 1
                    stats[cat]['by_score'][score_key]['correct'] += 1
            else:
                stats['No Favorite']['total'] += 1
                if pred.predicted_winner == pred.actual_winner:
                    stats['No Favorite']['correct'] += 1
        
        # Calculate accuracies
        for cat, cat_stats in stats.items():
            if cat_stats['total'] > 0:
                cat_stats['accuracy'] = (cat_stats['correct'] / cat_stats['total']) * 100
            
            if 'by_score' in cat_stats:
                for score, score_stats in cat_stats['by_score'].items():
                    if score_stats['completed'] > 0:
                        score_stats['accuracy'] = (score_stats['correct'] / score_stats['completed']) * 100
        
        return stats
    
    def _analyze_recent_performance(self) -> Dict:
        """Analyze recent prediction trends"""
        recent = [p for p in self.predictions[-20:] if p.actual_winner is not None]
        
        if not recent:
            return {'recent_accuracy': 0.0, 'trend': 'No data', 'best_score': None}
        
        # Overall recent accuracy
        recent_correct = sum(1 for p in recent if p.predicted_winner == p.actual_winner)
        recent_accuracy = (recent_correct / len(recent)) * 100
        
        # Trend analysis (compare first half vs second half)
        mid = len(recent) // 2
        first_half = recent[:mid]
        second_half = recent[mid:]
        
        first_acc = sum(1 for p in first_half if p.predicted_winner == p.actual_winner) / len(first_half) * 100 if first_half else 0
        second_acc = sum(1 for p in second_half if p.predicted_winner == p.actual_winner) / len(second_half) * 100 if second_half else 0
        
        if second_acc > first_acc + 10:
            trend = "Improving ↑"
        elif second_acc < first_acc - 10:
            trend = "Declining ↓"
        else:
            trend = "Stable →"
        
        # Best performing score in recent predictions
        score_perf = defaultdict(lambda: {'total': 0, 'correct': 0})
        for pred in recent:
            score = f"{pred.score[0]}-{pred.score[1]}"
            score_perf[score]['total'] += 1
            if pred.predicted_winner == pred.actual_winner:
                score_perf[score]['correct'] += 1
        
        best_score = None
        best_acc = 0
        for score, perf in score_perf.items():
            if perf['total'] >= 2:  # Minimum 2 predictions
                acc = (perf['correct'] / perf['total']) * 100
                if acc > best_acc:
                    best_acc = acc
                    best_score = score
        
        return {
            'recent_accuracy': recent_accuracy,
            'trend': trend,
            'best_score': best_score,
            'best_score_accuracy': best_acc
        }
    
    def _generate_betting_recommendations(self) -> List[str]:
        """Generate specific betting recommendations based on analysis"""
        recommendations = []
        
        # Analyze best performing scenarios
        score_set_stats = self._get_score_set_combination_stats()
        best_scenarios = sorted(
            [(k, v) for k, v in score_set_stats.items() if v['completed'] >= 3],
            key=lambda x: x[1]['accuracy'],
            reverse=True
        )[:3]
        
        if best_scenarios and best_scenarios[0][1]['accuracy'] >= 70:
            score, set_num = best_scenarios[0][0]
            recommendations.append(
                f"STRONG BET: {score} in Set {set_num} "
                f"(Accuracy: {best_scenarios[0][1]['accuracy']:.0f}%, "
                f"Sample: {best_scenarios[0][1]['completed']} games)"
            )
        
        # Confidence band recommendations
        conf_bands = self._analyze_confidence_bands()
        for band, stats in conf_bands.items():
            if stats['total'] >= 5 and stats['accuracy'] >= 65:
                recommendations.append(
                    f"Target predictions with {band} confidence "
                    f"({stats['accuracy']:.0f}% success rate)"
                )
        
        # Momentum recommendations
        momentum_stats = self._analyze_momentum_patterns()
        for momentum, stats in momentum_stats.items():
            if stats['total'] >= 5:
                if momentum == 'strong_serving' and stats['accuracy'] >= 65:
                    recommendations.append(
                        f"BET on players with strong serving momentum "
                        f"({stats['accuracy']:.0f}% accuracy)"
                    )
                elif momentum in ['weak_serving', 'momentum_shift'] and stats['accuracy'] < 40:
                    recommendations.append(
                        f"FADE players with {momentum.replace('_', ' ')} "
                        f"(Only {stats['accuracy']:.0f}% accuracy)"
                    )
        
        # Set-specific recommendations
        set_stats = self.get_statistics_by_set()
        for set_num, stats in set_stats.items():
            if stats['total'] >= 10 and stats['accuracy'] >= 65:
                recommendations.append(
                    f"Set {set_num} shows strong predictability "
                    f"({stats['accuracy']:.0f}% accuracy)"
                )
        
        if not recommendations:
            recommendations.append("Insufficient data for strong recommendations yet. Continue tracking.")
        
        return recommendations
    
    def _identify_avoid_scenarios(self) -> List[str]:
        """Identify scenarios with poor prediction performance"""
        avoid = []
        
        # Poor performing score-set combinations
        score_set_stats = self._get_score_set_combination_stats()
        poor_scenarios = [
            (k, v) for k, v in score_set_stats.items() 
            if v['completed'] >= 3 and v['accuracy'] < 40
        ]
        
        for (score, set_num), stats in poor_scenarios:
            avoid.append(
                f"{score} in Set {set_num} "
                f"(Only {stats['accuracy']:.0f}% accuracy in {stats['completed']} games)"
            )
        
        # Low confidence predictions
        conf_bands = self._analyze_confidence_bands()
        for band, stats in conf_bands.items():
            if stats['total'] >= 5 and stats['accuracy'] < 45:
                avoid.append(
                    f"Predictions with {band} confidence "
                    f"({stats['accuracy']:.0f}% accuracy)"
                )
        
        # Specific momentum situations
        momentum_stats = self._analyze_momentum_patterns()
        if 'momentum_shift' in momentum_stats and momentum_stats['momentum_shift']['total'] >= 3:
            if momentum_stats['momentum_shift']['accuracy'] < 35:
                avoid.append(
                    f"Servers showing momentum shift patterns "
                    f"({momentum_stats['momentum_shift']['accuracy']:.0f}% accuracy)"
                )
        
        # Set 3 if poor performance
        set_stats = self.get_statistics_by_set()
        if 3 in set_stats and set_stats[3]['total'] >= 3 and set_stats[3]['accuracy'] < 40:
            avoid.append(
                f"Deciding Set 3 predictions "
                f"({set_stats[3]['accuracy']:.0f}% accuracy)"
            )
        
        if not avoid:
            avoid.append("No clear patterns to avoid yet. Continue monitoring.")
        
        return avoid